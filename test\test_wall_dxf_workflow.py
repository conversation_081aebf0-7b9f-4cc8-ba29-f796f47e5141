#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试wall.dxf文件的完整工作流
按照用户指定的操作步骤进行测试
"""

import os
import sys
import time
import tkinter as tk
from tkinter import filedialog, messagebox

def test_wall_dxf_workflow():
    """测试wall.dxf文件的完整工作流"""
    print("🚀 启动wall.dxf文件工作流测试")
    print("="*80)
    
    try:
        # 检查文件是否存在
        dxf_file_path = r"C:\A-BCXM\cad文件\cs-wall\wall.dxf"
        if not os.path.exists(dxf_file_path):
            print(f"❌ 文件不存在: {dxf_file_path}")
            return False
        
        print(f"✅ 找到测试文件: {dxf_file_path}")
        
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建应用实例
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用初始化完成")
        
        # 步骤1：选择文件夹
        print("\n" + "="*60)
        print("📁 步骤1：选择文件夹")
        print("="*60)
        
        folder_path = os.path.dirname(dxf_file_path)
        print(f"选择文件夹: {folder_path}")
        
        # 模拟选择文件夹
        app.folder_path = folder_path
        if hasattr(app, 'folder_var'):
            app.folder_var.set(folder_path)
        
        print(f"✅ 文件夹选择完成")
        
        # 步骤2：开始处理
        print("\n" + "="*60)
        print("🔄 步骤2：开始处理")
        print("="*60)
        
        # 模拟点击开始处理按钮
        if hasattr(app, 'start_processing'):
            print("调用start_processing方法...")
            try:
                app.start_processing()
                print("✅ 开始处理调用成功")
            except Exception as e:
                print(f"❌ 开始处理失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("❌ 找不到start_processing方法")
            return False
        
        # 等待处理完成
        print("⏳ 等待文件处理完成...")
        time.sleep(3)
        
        # 检查处理结果
        if hasattr(app, 'processor') and app.processor:
            if hasattr(app.processor, 'current_file_entities'):
                entity_count = len(app.processor.current_file_entities) if app.processor.current_file_entities else 0
                print(f"📊 处理结果: 加载了 {entity_count} 个实体")
                
                if entity_count > 0:
                    print("✅ 文件处理成功")
                else:
                    print("⚠️ 文件处理完成但没有实体")
            else:
                print("⚠️ 处理器没有实体数据")
        else:
            print("❌ 处理器未初始化")
            return False
        
        # 步骤3：线条处理
        print("\n" + "="*60)
        print("📏 步骤3：线条处理")
        print("="*60)
        
        # 检查线条处理状态
        if hasattr(app, 'processor') and app.processor:
            print("检查线条处理状态...")
            
            # 检查是否有线条处理相关的方法
            if hasattr(app.processor, 'process_lines') or hasattr(app.processor, 'merge_lines'):
                print("✅ 线条处理功能可用")
            else:
                print("⚠️ 线条处理功能可能已自动完成")
        
        # 步骤4：识别分组
        print("\n" + "="*60)
        print("🔍 步骤4：识别分组")
        print("="*60)
        
        # 模拟点击识别分组按钮
        if hasattr(app, 'start_grouping'):
            print("调用start_grouping方法...")
            try:
                app.start_grouping()
                print("✅ 识别分组调用成功")
            except Exception as e:
                print(f"❌ 识别分组失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("❌ 找不到start_grouping方法")
            return False
        
        # 等待分组完成
        print("⏳ 等待分组识别完成...")
        time.sleep(5)
        
        # 检查分组结果
        if hasattr(app, 'processor') and app.processor:
            if hasattr(app.processor, 'all_groups'):
                group_count = len(app.processor.all_groups) if app.processor.all_groups else 0
                print(f"📊 分组结果: 识别了 {group_count} 个组")
                
                if group_count > 0:
                    print("✅ 分组识别成功")
                    
                    # 显示分组详情
                    for i, group in enumerate(app.processor.all_groups):
                        entity_count = len(group) if isinstance(group, list) else 0
                        print(f"  组{i+1}: {entity_count} 个实体")
                else:
                    print("⚠️ 分组识别完成但没有组")
            else:
                print("⚠️ 处理器没有分组数据")
        
        # 步骤5：选择类别（家具）
        print("\n" + "="*60)
        print("🏷️ 步骤5：选择类别（家具）")
        print("="*60)
        
        # 检查是否进入手动分类模式
        if hasattr(app, 'processor') and app.processor and hasattr(app.processor, 'all_groups'):
            if app.processor.all_groups and len(app.processor.all_groups) > 0:
                print("进入手动分类模式...")
                
                # 显示第一个组
                first_group = app.processor.all_groups[0]
                print(f"显示第一个组: {len(first_group)} 个实体")
                
                # 调用_show_group方法
                if hasattr(app, '_show_group'):
                    try:
                        app._show_group(first_group, 0)
                        print("✅ 组显示成功")
                        
                        # 检查颜色显示
                        print("\n🎨 检查颜色显示:")
                        test_color_display(app, first_group)
                        
                    except Exception as e:
                        print(f"❌ 组显示失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 模拟选择家具类别
                print("\n🏷️ 模拟选择家具类别...")
                try:
                    # 模拟手动标注为家具
                    label = "furniture"
                    group_index = 0
                    
                    # 更新实体标签
                    for entity in first_group:
                        if isinstance(entity, dict):
                            entity['label'] = label
                            entity['auto_labeled'] = False
                    
                    # 添加到已标注列表
                    if hasattr(app.processor, 'labeled_entities'):
                        if not app.processor.labeled_entities:
                            app.processor.labeled_entities = []
                        app.processor.labeled_entities.extend(first_group)
                    
                    # 触发状态更新
                    if hasattr(app, 'on_status_update'):
                        app.on_status_update("group_labeled", (group_index, label))
                    
                    print(f"✅ 成功标注为家具类别")
                    
                    # 检查标注后的颜色显示
                    print("\n🎨 检查标注后颜色显示:")
                    test_color_display_after_labeling(app, first_group, label)
                    
                except Exception as e:
                    print(f"❌ 家具类别选择失败: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print("❌ 没有可用的组进行分类")
        else:
            print("❌ 无法进入手动分类模式")
        
        print("\n" + "="*80)
        print("📊 测试完成")
        print("="*80)
        
        # 清理
        try:
            root.destroy()
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_display(app, group):
    """测试颜色显示"""
    try:
        if hasattr(app, 'processor') and hasattr(app.processor, 'visualizer'):
            visualizer = app.processor.visualizer
            
            if hasattr(visualizer, '_get_entity_display_info_enhanced'):
                print("  🔍 检查实体颜色:")
                
                for i, entity in enumerate(group[:3]):  # 只检查前3个实体
                    if isinstance(entity, dict):
                        try:
                            color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                                entity,
                                labeled_entities=getattr(app.processor, 'labeled_entities', []),
                                current_group_entities=group,
                                processor=app.processor
                            )
                            
                            entity_type = entity.get('type', 'UNKNOWN')
                            layer = entity.get('layer', 'UNKNOWN')
                            
                            print(f"    实体{i+1} ({entity_type}, {layer}):")
                            print(f"      颜色: {color}")
                            print(f"      透明度: {alpha}")
                            print(f"      状态: {status}")
                            
                            # 检查是否使用了墙体颜色
                            if color == '#8B4513':
                                print(f"      ⚠️ 发现问题：使用了墙体颜色")
                            elif color == '#FF0000':
                                print(f"      ✅ 正确：使用了红色高亮")
                            else:
                                print(f"      ℹ️ 使用了其他颜色")
                                
                        except Exception as e:
                            print(f"    ❌ 实体{i+1}颜色检查失败: {e}")
            else:
                print("  ❌ 增强颜色显示方法不存在")
        else:
            print("  ❌ 处理器或可视化器未初始化")
            
    except Exception as e:
        print(f"  ❌ 颜色显示测试失败: {e}")

def test_color_display_after_labeling(app, group, label):
    """测试标注后的颜色显示"""
    try:
        if hasattr(app, 'processor') and hasattr(app.processor, 'visualizer'):
            visualizer = app.processor.visualizer
            
            if hasattr(visualizer, '_get_entity_display_info_enhanced'):
                print("  🔍 检查标注后实体颜色:")
                
                for i, entity in enumerate(group[:3]):  # 只检查前3个实体
                    if isinstance(entity, dict):
                        try:
                            color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                                entity,
                                labeled_entities=getattr(app.processor, 'labeled_entities', []),
                                current_group_entities=group,
                                processor=app.processor
                            )
                            
                            entity_type = entity.get('type', 'UNKNOWN')
                            entity_label = entity.get('label', 'None')
                            
                            print(f"    实体{i+1} ({entity_type}):")
                            print(f"      标签: {entity_label}")
                            print(f"      颜色: {color}")
                            print(f"      状态: {status}")
                            
                            # 验证标注结果
                            if entity_label == label:
                                print(f"      ✅ 标签更新正确")
                            else:
                                print(f"      ❌ 标签更新失败")
                            
                            # 检查颜色变化
                            if status == 'current' and color == '#FF0000':
                                print(f"      ✅ 当前组高亮正确")
                            elif status == 'labeled' and color != '#808080':
                                print(f"      ✅ 标注后分类颜色正确")
                            else:
                                print(f"      ⚠️ 颜色状态需要检查")
                                
                        except Exception as e:
                            print(f"    ❌ 实体{i+1}标注后颜色检查失败: {e}")
            else:
                print("  ❌ 增强颜色显示方法不存在")
        else:
            print("  ❌ 处理器或可视化器未初始化")
            
    except Exception as e:
        print(f"  ❌ 标注后颜色显示测试失败: {e}")

def main():
    """主函数"""
    success = test_wall_dxf_workflow()
    
    if success:
        print("\n🎉 wall.dxf工作流测试完成！")
    else:
        print("\n❌ wall.dxf工作流测试失败！")
    
    return success

if __name__ == "__main__":
    main()
