#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简单去重功能
验证修改后的简单去重方式是否正常工作
"""

import os
import sys
import time
import json
from typing import List, Dict, Any

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from selective_layer_processor import SelectiveLayerProcessor
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    MODULES_AVAILABLE = False


def create_simple_dedup_test_data() -> List[Dict[str, Any]]:
    """创建简单去重测试数据"""
    entities = []
    
    # 测试1: 完全相同的线条实体
    entities.extend([
        {
            'id': 'line_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [10, 20],
            'end_point': [30, 20],
            'color': 2,
            'points': [[10, 20], [30, 20]]
        },
        {
            'id': 'line_1_duplicate',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [10, 20],
            'end_point': [30, 20],
            'color': 2,
            'points': [[10, 20], [30, 20]]
        }
    ])
    
    # 测试2: 微小差异的线条实体（应该被认为是重复）
    entities.extend([
        {
            'id': 'line_2',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [50, 20],
            'end_point': [70, 20],
            'color': 2,
            'points': [[50, 20], [70, 20]]
        },
        {
            'id': 'line_2_slight_diff',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [50.005, 20.003],  # 微小差异，在0.01容差内
            'end_point': [70.002, 20.001],
            'color': 2,
            'points': [[50.005, 20.003], [70.002, 20.001]]
        }
    ])
    
    # 测试3: 明显不同的线条实体（不应该被去重）
    entities.extend([
        {
            'id': 'line_3',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [100, 20],
            'end_point': [120, 20],
            'color': 2,
            'points': [[100, 20], [120, 20]]
        },
        {
            'id': 'line_4',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [100, 30],  # 明显不同的位置
            'end_point': [120, 30],
            'color': 2,
            'points': [[100, 30], [120, 30]]
        }
    ])
    
    # 测试4: 相同的文字实体
    entities.extend([
        {
            'id': 'text_1',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [50, 50],
            'height': 3,
            'color': 3
        },
        {
            'id': 'text_1_duplicate',
            'type': 'TEXT',
            'layer': 'A-TEXT',
            'text': '房间1',
            'position': [50, 50],
            'height': 3,
            'color': 3
        }
    ])
    
    # 测试5: 相同的圆形实体
    entities.extend([
        {
            'id': 'circle_1',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [100, 100],
            'radius': 5,
            'color': 4
        },
        {
            'id': 'circle_1_duplicate',
            'type': 'CIRCLE',
            'layer': 'EQUIPMENT',
            'center': [100, 100],
            'radius': 5,
            'color': 4
        }
    ])
    
    # 测试6: 不同颜色的相同实体（应该被认为是不同的）
    entities.extend([
        {
            'id': 'line_diff_color_1',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [200, 20],
            'end_point': [220, 20],
            'color': 2,
            'points': [[200, 20], [220, 20]]
        },
        {
            'id': 'line_diff_color_2',
            'type': 'LINE',
            'layer': 'A-DOOR',
            'start_point': [200, 20],
            'end_point': [220, 20],
            'color': 3,  # 不同颜色
            'points': [[200, 20], [220, 20]]
        }
    ])
    
    return entities


def test_simple_duplicate_detection():
    """测试简单重复检测"""
    print("🧪 测试简单重复检测")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    processor = SelectiveLayerProcessor()
    
    # 测试数据
    test_cases = [
        # 完全相同
        ({
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1
        }, {
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1
        }, True, "完全相同的线条"),
        
        # 微小差异（在容差内）
        ({
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1
        }, {
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0.005, 0.003],
            'end_point': [10.002, 0.001],
            'color': 1
        }, True, "微小差异（容差内）"),
        
        # 明显不同
        ({
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1
        }, {
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 10],
            'end_point': [10, 10],
            'color': 1
        }, False, "明显不同的位置"),
        
        # 不同颜色
        ({
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 1
        }, {
            'type': 'LINE',
            'layer': 'TEST',
            'start_point': [0, 0],
            'end_point': [10, 0],
            'color': 2
        }, False, "不同颜色"),
        
        # 文字实体
        ({
            'type': 'TEXT',
            'layer': 'TEST',
            'text': '测试',
            'position': [10, 10],
            'color': 1
        }, {
            'type': 'TEXT',
            'layer': 'TEST',
            'text': '测试',
            'position': [10, 10],
            'color': 1
        }, True, "相同文字实体")
    ]
    
    print("📊 重复检测测试:")
    for i, (entity1, entity2, expected, description) in enumerate(test_cases):
        result = processor._is_simple_duplicate(entity1, entity2)
        status = "✅" if result == expected else "❌"
        print(f"   {i+1}. {description}: {status} (预期: {expected}, 实际: {result})")
    
    return True


def test_simple_deduplication_process():
    """测试简单去重处理过程"""
    print("\n🧪 测试简单去重处理过程")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用")
        return None
    
    test_entities = create_simple_dedup_test_data()
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    
    # 按图层统计输入数据
    input_stats = {}
    for entity in test_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in input_stats:
            input_stats[layer] = 0
        input_stats[layer] += 1
    
    print("📋 输入数据统计:")
    for layer, count in input_stats.items():
        print(f"   {layer}: {count} 个实体")
    
    # 创建处理器并执行处理
    processor = SelectiveLayerProcessor()
    
    print(f"\n🔄 执行简单去重处理...")
    start_time = time.time()
    
    processed_entities = processor.process_entities(test_entities)
    
    processing_time = time.time() - start_time
    
    print(f"\n📈 处理结果:")
    print(f"   处理时间: {processing_time:.3f} 秒")
    print(f"   输入实体: {len(test_entities)}")
    print(f"   输出实体: {len(processed_entities)}")
    
    # 按图层统计输出数据
    output_stats = {}
    processing_types = {}
    for entity in processed_entities:
        layer = entity.get('layer', 'UNKNOWN')
        if layer not in output_stats:
            output_stats[layer] = 0
        output_stats[layer] += 1
        
        proc_type = entity.get('processing_type', 'unknown')
        if proc_type not in processing_types:
            processing_types[proc_type] = 0
        processing_types[proc_type] += 1
    
    print("\n📋 输出数据统计:")
    for layer, count in output_stats.items():
        input_count = input_stats.get(layer, 0)
        removed = input_count - count
        print(f"   {layer}: {count} 个实体 (移除 {removed} 个)")
    
    print("\n📊 处理方式统计:")
    for proc_type, count in processing_types.items():
        print(f"   {proc_type}: {count} 个实体")
    
    # 详细分析去重效果
    print(f"\n🔍 去重效果分析:")
    total_removed = len(test_entities) - len(processed_entities)
    print(f"   总移除实体: {total_removed} 个")
    print(f"   去重率: {total_removed / len(test_entities) * 100:.1f}%")
    
    # 验证特定的去重案例
    print(f"\n📝 验证去重案例:")
    
    # 检查是否正确识别了简单去重
    simple_dedup_count = sum(1 for e in processed_entities 
                           if e.get('processing_type') == 'simple_deduplication')
    print(f"   简单去重实体: {simple_dedup_count} 个")
    
    return {
        'input_entities': len(test_entities),
        'output_entities': len(processed_entities),
        'processing_time': processing_time,
        'removed_entities': total_removed,
        'dedup_rate': total_removed / len(test_entities),
        'input_stats': input_stats,
        'output_stats': output_stats,
        'processing_types': processing_types
    }


def run_simple_deduplication_test():
    """运行简单去重测试"""
    print("🚀 开始简单去重测试")
    print("=" * 80)
    
    if not MODULES_AVAILABLE:
        print("❌ 模块不可用，无法运行测试")
        return False
    
    start_time = time.time()
    
    results = {
        'test_time': time.time(),
        'duplicate_detection_test': None,
        'deduplication_process_test': None,
        'summary': {}
    }
    
    try:
        # 1. 测试重复检测逻辑
        results['duplicate_detection_test'] = test_simple_duplicate_detection()
        
        # 2. 测试去重处理过程
        results['deduplication_process_test'] = test_simple_deduplication_process()
        
        # 3. 生成总结
        total_time = time.time() - start_time
        
        process_result = results['deduplication_process_test']
        
        results['summary'] = {
            'total_test_time': total_time,
            'detection_test_success': results['duplicate_detection_test'] is not None,
            'process_test_success': process_result is not None,
            'all_tests_passed': results['duplicate_detection_test'] and process_result,
            'dedup_rate': process_result['dedup_rate'] if process_result else 0,
            'processing_time': process_result['processing_time'] if process_result else 0,
            'performance_good': process_result['processing_time'] < 0.1 if process_result else False
        }
        
        print(f"\n🎉 简单去重测试完成")
        print("=" * 80)
        print(f"   总耗时: {total_time:.2f} 秒")
        print(f"   重复检测测试: {'通过' if results['summary']['detection_test_success'] else '失败'}")
        print(f"   去重处理测试: {'通过' if results['summary']['process_test_success'] else '失败'}")
        if process_result:
            print(f"   去重率: {results['summary']['dedup_rate']:.1%}")
            print(f"   处理性能: {'良好' if results['summary']['performance_good'] else '需优化'}")
        print(f"   所有测试通过: {'是' if results['summary']['all_tests_passed'] else '否'}")
        
        # 保存测试结果
        with open('simple_deduplication_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"   测试结果已保存到: simple_deduplication_test_results.json")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_simple_deduplication_test()
    sys.exit(0 if success else 1)
