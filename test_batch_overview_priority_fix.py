#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试批量概览颜色优先级修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_color_priority_consistency():
    """测试颜色优先级一致性"""
    
    print("🎨 测试批量概览颜色优先级修复")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 模拟真实的配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'furniture': '#4DB6AC',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'pending': '#FFA500',
            'other': '#808080'
        }
        
        # 测试不同状态的组信息
        test_cases = [
            {
                'name': '当前组（最高优先级）',
                'group_info': {'status': 'labeled', 'label': 'wall', 'is_current_group': True},
                'expected_color': '#FF0000'
            },
            {
                'name': '标注中状态',
                'group_info': {'status': 'labeling', 'label': '未标注', 'is_current_group': False},
                'expected_color': '#FF0000'
            },
            {
                'name': '自动标注的墙体',
                'group_info': {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},
                'expected_color': '#8B4513'
            },
            {
                'name': '自动标注的门窗',
                'group_info': {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False},
                'expected_color': '#FFD700'
            },
            {
                'name': '手动标注的家具',
                'group_info': {'status': 'labeled', 'label': 'furniture', 'is_current_group': False},
                'expected_color': '#4DB6AC'
            },
            {
                'name': '未标注状态',
                'group_info': {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False},
                'expected_color': '#D3D3D3'
            },
            {
                'name': '待处理状态',
                'group_info': {'status': 'pending', 'label': '未标注', 'is_current_group': False},
                'expected_color': '#FFA500'
            },
            {
                'name': '未知状态',
                'group_info': {'status': 'unknown', 'label': 'unknown', 'is_current_group': False},
                'expected_color': '#808080'
            }
        ]
        
        print("🧪 测试各种状态的颜色获取:")
        all_passed = True
        
        for i, test_case in enumerate(test_cases):
            print(f"\n  测试 {i+1}: {test_case['name']}")
            print(f"    组信息: {test_case['group_info']}")
            
            actual_color = fix._get_color_from_group_info(test_case['group_info'], color_scheme)
            expected_color = test_case['expected_color']
            
            print(f"    实际颜色: {actual_color}")
            print(f"    期望颜色: {expected_color}")
            
            if actual_color == expected_color:
                print(f"    ✅ 通过")
            else:
                print(f"    ❌ 失败")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_scenario_simulation():
    """模拟真实场景测试"""
    
    print("\n🌍 模拟真实场景测试")
    print("=" * 40)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 模拟您提供的真实配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'furniture': '#4DB6AC',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'labeling': '#FF0000',
            'other': '#808080'
        }
        
        # 模拟您提供的真实组信息
        real_groups_info = [
            {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},      # 组0-15: 墙体
            {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False}, # 组16-46: 门窗
            {'status': 'labeling', 'label': '未标注', 'is_current_group': True},          # 组47: 标注中
            {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}        # 组48-52: 未标注
        ]
        
        print("📊 真实场景颜色测试:")
        
        for i, group_info in enumerate(real_groups_info):
            color = fix._get_color_from_group_info(group_info, color_scheme)
            
            if group_info['status'] == 'auto_labeled' and group_info['label'] == 'wall':
                group_type = "墙体组"
                expected = "棕色 (#8B4513)"
            elif group_info['status'] == 'auto_labeled' and group_info['label'] == 'door_window':
                group_type = "门窗组"
                expected = "金色 (#FFD700)"
            elif group_info['status'] == 'labeling':
                group_type = "标注中组"
                expected = "红色 (#FF0000)"
            elif group_info['status'] == 'unlabeled':
                group_type = "未标注组"
                expected = "灰色 (#D3D3D3)"
            else:
                group_type = "其他组"
                expected = "未知"
            
            print(f"  {group_type}: {color} (期望: {expected})")
        
        print("\n🎯 预期效果:")
        print("  - 墙体组 (auto_labeled + wall): 显示棕色")
        print("  - 门窗组 (auto_labeled + door_window): 显示金色")
        print("  - 标注中组 (labeling): 显示红色")
        print("  - 未标注组 (unlabeled): 显示灰色")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_priority_summary():
    """提供优先级总结"""
    
    print("\n📋 颜色优先级总结")
    print("=" * 30)
    
    print("🔧 修复后的颜色优先级（从高到低）:")
    print("  1. 🔴 当前组 (is_current_group=True) → 红色 (#FF0000)")
    print("  2. 🔴 标注中 (status=labeling) → 红色 (#FF0000)")
    print("  3. 🎨 已标注 (status=labeled/auto_labeled) → 根据标签颜色")
    print("     - 墙体 (wall) → 棕色 (#8B4513)")
    print("     - 门窗 (door_window) → 金色 (#FFD700)")
    print("     - 家具 (furniture) → 青色 (#4DB6AC)")
    print("     - 其他标签 → 对应颜色")
    print("  4. 🔘 未标注 (status=unlabeled) → 灰色 (#D3D3D3)")
    print("  5. 🟠 待处理 (status=pending) → 橙色 (#FFA500)")
    print("  6. ⚫ 其他状态 → 深灰色 (#808080)")
    
    print("\n✅ 修复要点:")
    print("  - 增加了 labeling 状态的处理")
    print("  - 扩展了标签到颜色的映射")
    print("  - 确保优先级与主程序一致")
    print("  - 使用正确的 unlabeled 颜色 (#D3D3D3)")

if __name__ == "__main__":
    print("🎨 批量概览颜色优先级修复测试")
    print("=" * 50)
    
    # 测试颜色优先级一致性
    priority_test = test_color_priority_consistency()
    
    # 模拟真实场景
    scenario_test = test_real_scenario_simulation()
    
    # 提供优先级总结
    provide_priority_summary()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结:")
    
    if priority_test and scenario_test:
        print("✅ 颜色优先级修复验证通过")
        print("✅ 真实场景模拟正常")
        print("✅ 批量概览现在应该显示正确颜色")
        print("\n🚀 修复完成！批量概览将按照正确的优先级显示颜色：")
        print("  - 墙体组：棕色")
        print("  - 门窗组：金色")
        print("  - 标注中组：红色")
        print("  - 未标注组：灰色")
    else:
        print("❌ 部分测试失败")
        if not priority_test:
            print("❌ 颜色优先级测试失败")
        if not scenario_test:
            print("❌ 真实场景测试失败")
