#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试索引图修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_legend_title_removal():
    """测试索引图标题移除"""
    
    print("🔍 测试索引图标题移除")
    print("=" * 40)
    
    try:
        # 检查main_enhanced_with_v2_fill.py中的标题设置
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否还有"索引图"标题
        title_lines = []
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if 'text="索引图"' in line:
                title_lines.append((i+1, line.strip()))
        
        if title_lines:
            print("❌ 仍有索引图标题设置:")
            for line_num, line in title_lines:
                print(f"  第{line_num}行: {line}")
            return False
        else:
            print("✅ 索引图标题已移除")
        
        # 检查是否还有分类标题（━━ 组状态 ━━）
        category_lines = []
        for i, line in enumerate(lines):
            if '━━' in line and ('组状态' in line or '实体类别' in line):
                category_lines.append((i+1, line.strip()))
        
        if category_lines:
            print("❌ 仍有分类标题:")
            for line_num, line in category_lines:
                print(f"  第{line_num}行: {line}")
            return False
        else:
            print("✅ 分类标题已移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_legend_method_implementation():
    """测试索引图方法实现"""
    
    print("\n🔧 测试索引图方法实现")
    print("=" * 40)
    
    try:
        # 检查_draw_legend_content方法
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_draw_legend_content方法
        start_pos = content.find('def _draw_legend_content')
        if start_pos == -1:
            print("❌ 未找到_draw_legend_content方法")
            return False
        
        end_pos = content.find('def ', start_pos + 1)
        if end_pos == -1:
            end_pos = len(content)
        
        method_content = content[start_pos:end_pos]
        
        # 检查修复标记
        fixes = []
        if '🔧 修复' in method_content:
            fixes.append("修复标记")
        if 'cols = 2' in method_content:
            fixes.append("2列网格布局")
        if '去除分类标题' in method_content or '无标题' in method_content:
            fixes.append("去除分类标题")
        if '网格布局' in method_content:
            fixes.append("网格布局")
        
        print(f"✅ _draw_legend_content方法包含修复: {', '.join(fixes)}")
        
        # 检查是否有分类标题代码
        if '━━ 组状态 ━━' in method_content or '━━ 实体类别 ━━' in method_content:
            print("❌ 方法中仍有分类标题代码")
            return False
        else:
            print("✅ 方法中无分类标题代码")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cad_visualizer_cleanup():
    """测试cad_visualizer.py清理"""
    
    print("\n🧹 测试cad_visualizer.py清理")
    print("=" * 40)
    
    try:
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_create_color_index_chart是否已弃用
        start_pos = content.find('def _create_color_index_chart')
        if start_pos == -1:
            print("❌ 未找到_create_color_index_chart方法")
            return False
        
        end_pos = content.find('def ', start_pos + 1)
        if end_pos == -1:
            end_pos = len(content)
        
        method_content = content[start_pos:end_pos]
        
        if '已弃用' in method_content and 'pass' in method_content:
            print("✅ _create_color_index_chart已正确弃用")
        else:
            print("❌ _create_color_index_chart可能仍有重复实现")
            return False
        
        # 检查update_color_index方法
        if 'def update_color_index' in content:
            print("⚠️ update_color_index方法仍存在，可能导致冲突")
            # 这个方法可能仍在被调用，需要进一步检查
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_legend_conflict():
    """分析索引图冲突原因"""
    
    print("\n🔍 分析索引图冲突原因")
    print("=" * 40)
    
    try:
        # 检查可能的调用路径
        print("📋 检查可能的索引图调用:")
        
        # 1. 检查main_enhanced_with_v2_fill.py中的调用
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        legend_calls = []
        lines = main_content.split('\n')
        for i, line in enumerate(lines):
            if ('_draw_legend_content' in line or 
                '_update_legend_display' in line or
                'update_color_index' in line):
                legend_calls.append((i+1, line.strip()))
        
        print(f"  main_enhanced_with_v2_fill.py中的索引图调用: {len(legend_calls)}个")
        for line_num, line in legend_calls[:5]:  # 只显示前5个
            print(f"    第{line_num}行: {line}")
        
        # 2. 检查cad_visualizer.py中的调用
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            cad_content = f.read()
        
        cad_legend_calls = []
        lines = cad_content.split('\n')
        for i, line in enumerate(lines):
            if ('update_color_index' in line or 
                '_create_color_index_chart' in line):
                cad_legend_calls.append((i+1, line.strip()))
        
        print(f"  cad_visualizer.py中的索引图调用: {len(cad_legend_calls)}个")
        for line_num, line in cad_legend_calls[:5]:  # 只显示前5个
            print(f"    第{line_num}行: {line}")
        
        # 分析可能的问题
        print("\n🎯 可能的问题:")
        if len(cad_legend_calls) > 2:  # 除了定义，还有其他调用
            print("  1. cad_visualizer.py中的update_color_index可能仍在被调用")
        
        if len(legend_calls) > 10:
            print("  2. main_enhanced_with_v2_fill.py中可能有多个索引图更新路径")
        
        print("  3. 可能存在多个索引图更新机制同时运行")
        print("  4. 界面初始化时可能设置了固定的标题")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def provide_solution():
    """提供解决方案"""
    
    print("\n💡 解决方案建议")
    print("=" * 40)
    
    print("基于分析，索引图问题可能的解决方案:")
    print()
    print("1. **完全禁用cad_visualizer.py中的索引图方法**")
    print("   - 将update_color_index方法也标记为弃用")
    print("   - 确保只使用main_enhanced_with_v2_fill.py中的实现")
    print()
    print("2. **检查界面初始化代码**")
    print("   - 确保没有在界面创建时设置固定标题")
    print("   - 移除所有硬编码的分类标题")
    print()
    print("3. **统一索引图更新机制**")
    print("   - 只保留一个索引图更新入口")
    print("   - 确保所有更新都通过_draw_legend_content方法")
    print()
    print("4. **强制刷新索引图**")
    print("   - 在应用修复后强制重新绘制索引图")
    print("   - 清除可能的缓存数据")

if __name__ == "__main__":
    print("🔍 索引图修复效果测试程序")
    print("=" * 50)
    
    # 运行测试
    test1 = test_legend_title_removal()
    test2 = test_legend_method_implementation()
    test3 = test_cad_visualizer_cleanup()
    
    # 分析冲突
    analyze_legend_conflict()
    
    # 提供解决方案
    provide_solution()
    
    print("\n" + "=" * 50)
    print("🎯 测试总结:")
    
    if test1 and test2 and test3:
        print("✅ 基础修复已完成")
        print("⚠️ 但可能仍有多个索引图机制冲突")
        print("💡 建议按照上述解决方案进一步修复")
    else:
        print("❌ 基础修复不完整")
        if not test1:
            print("❌ 索引图标题未完全移除")
        if not test2:
            print("❌ 索引图方法实现有问题")
        if not test3:
            print("❌ cad_visualizer.py清理不完整")
