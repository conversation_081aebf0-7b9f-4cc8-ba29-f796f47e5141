#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试概览图缓存修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 导入修复后的可视化器
from cad_visualizer import CADVisualizer

class MockProcessor:
    """模拟处理器，用于测试"""
    
    def __init__(self):
        self.all_groups = []
        self.groups_info = []
        
    def set_test_data(self, groups, groups_info):
        """设置测试数据"""
        self.all_groups = groups
        self.groups_info = groups_info
        print(f"设置测试数据: {len(groups)} 个组, {len(groups_info)} 个组信息")

def test_overview_cache_fix():
    """测试概览图缓存修复"""
    
    print("🔧 概览图缓存修复测试")
    print("=" * 50)
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("概览图缓存修复测试")
    root.geometry("1200x800")
    
    try:
        # 创建修复后的可视化器
        visualizer = CADVisualizer()
        
        # 设置测试配色方案
        test_color_scheme = {
            'wall': '#FF6B6B',
            'door_window': '#4ECDC4', 
            'furniture': '#45B7D1',
            'column': '#96CEB4',
            'stair': '#FFEAA7',
            'other': '#DDA0DD',
            'current_group': '#FF0000',
            'labeled': '#00AA00',
            'unlabeled': '#A0A0A0',
            'text': '#000000'
        }
        
        visualizer.update_color_scheme(test_color_scheme)
        
        # 创建模拟处理器
        mock_processor = MockProcessor()
        
        # 🔧 测试1：第一次设置数据
        print("\n🧪 测试1: 第一次设置数据")
        
        # 创建第一组测试数据
        test_groups_1 = [
            [{'type': 'LINE', 'start': [0, 0], 'end': [100, 0]}],  # 组1
            [{'type': 'LINE', 'start': [0, 100], 'end': [100, 100]}],  # 组2
        ]
        
        test_groups_info_1 = [
            {'status': 'labeled', 'label': 'wall', 'display_color': '#FF6B6B'},
            {'status': 'unlabeled', 'label': '未标注', 'display_color': '#A0A0A0'},
        ]
        
        mock_processor.set_test_data(test_groups_1, test_groups_info_1)
        
        # 第一次可视化
        visualizer.visualize_overview(
            all_entities=[],  # 不使用实体列表
            processor=mock_processor,
            current_group_index=0
        )
        
        print("✅ 第一次可视化完成")
        
        # 🔧 测试2：更改数据并重新可视化
        print("\n🧪 测试2: 更改数据并重新可视化")
        
        # 创建第二组测试数据（不同的状态和标签）
        test_groups_2 = [
            [{'type': 'LINE', 'start': [0, 0], 'end': [100, 0]}],  # 组1
            [{'type': 'LINE', 'start': [0, 100], 'end': [100, 100]}],  # 组2
            [{'type': 'LINE', 'start': [200, 0], 'end': [300, 0]}],  # 组3（新增）
        ]
        
        test_groups_info_2 = [
            {'status': 'labeled', 'label': 'furniture', 'display_color': '#45B7D1'},  # 改变标签
            {'status': 'labeled', 'label': 'door_window', 'display_color': '#4ECDC4'},  # 改变状态
            {'status': 'unlabeled', 'label': '未标注', 'display_color': '#A0A0A0'},  # 新增组
        ]
        
        mock_processor.set_test_data(test_groups_2, test_groups_info_2)
        
        # 第二次可视化（应该显示新的颜色和状态）
        visualizer.visualize_overview(
            all_entities=[],  # 不使用实体列表
            processor=mock_processor,
            current_group_index=1  # 改变当前组
        )
        
        print("✅ 第二次可视化完成")
        
        # 🔧 测试3：验证缓存清除
        print("\n🧪 测试3: 验证缓存清除")
        
        # 检查概览图轴上的对象数量
        overview_objects = len(visualizer.ax_overview.get_children())
        print(f"概览图对象数量: {overview_objects}")
        
        # 检查颜色缓存
        if hasattr(visualizer, 'used_colors'):
            print(f"颜色缓存项目数量: {len(visualizer.used_colors)}")
            for color_key, color_info in visualizer.used_colors.items():
                print(f"  - {color_key}: {color_info}")
        
        # 创建画布并显示
        canvas = FigureCanvasTkAgg(visualizer.get_figure(), root)
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # 添加测试结果说明
        info_frame = tk.Frame(root)
        info_frame.pack(fill='x', pady=5)
        
        info_text = """
缓存修复测试结果：
✅ 修复1: 强制清除缓存 - 每次刷新都清除所有matplotlib对象
✅ 修复2: 强制获取最新数据 - 直接从处理器获取最新的组数据
✅ 修复3: 重新计算颜色 - 不使用任何颜色缓存，强制重新计算
✅ 修复4: 数据一致性验证 - 检查组数据和组信息的一致性
✅ 修复5: 详细日志输出 - 显示每个组的状态、标签和颜色信息

测试步骤：
1. 第一次设置数据：2个组，组1为墙体(已标注)，组2为未标注
2. 第二次设置数据：3个组，改变标签和状态，新增组3
3. 验证：概览图应该显示最新的颜色和状态，无缓存残留
        """
        
        info_label = tk.Label(info_frame, text=info_text, 
                             font=('Arial', 9), justify='left')
        info_label.pack(anchor='w', padx=10)
        
        # 添加关闭按钮
        close_btn = tk.Button(root, text="关闭测试", 
                             command=root.destroy,
                             font=('Arial', 10))
        close_btn.pack(pady=5)
        
        print("\n✅ 概览图缓存修复测试完成")
        print("📋 修复总结：")
        print("   1. 强制清除所有matplotlib对象缓存")
        print("   2. 强制从处理器获取最新数据")
        print("   3. 重新计算所有颜色，不使用缓存")
        print("   4. 验证数据一致性")
        print("   5. 详细日志输出，便于调试")
        
        # 启动测试窗口
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 概览图缓存修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("测试失败", f"概览图缓存修复测试失败: {e}")

def test_cache_clearing_only():
    """专门测试缓存清除功能"""
    
    print("\n🧪 缓存清除专项测试")
    print("-" * 30)
    
    try:
        visualizer = CADVisualizer()
        
        # 设置配色方案
        test_color_scheme = {
            'wall': '#FF6B6B',
            'furniture': '#45B7D1',
            'current_group': '#FF0000',
            'labeled': '#00AA00',
            'unlabeled': '#A0A0A0'
        }
        visualizer.update_color_scheme(test_color_scheme)
        
        # 创建模拟数据
        mock_processor = MockProcessor()
        test_groups = [
            [{'type': 'LINE', 'start': [0, 0], 'end': [100, 0]}]
        ]
        test_groups_info = [
            {'status': 'labeled', 'label': 'wall', 'display_color': '#FF6B6B'}
        ]
        mock_processor.set_test_data(test_groups, test_groups_info)
        
        # 第一次可视化
        print("第一次可视化...")
        visualizer.visualize_overview(all_entities=[], processor=mock_processor)
        first_objects = len(visualizer.ax_overview.get_children())
        print(f"第一次可视化后，概览图对象数量: {first_objects}")
        
        # 测试缓存清除
        print("测试缓存清除...")
        visualizer._clear_overview_cache()
        after_clear_objects = len(visualizer.ax_overview.get_children())
        print(f"缓存清除后，概览图对象数量: {after_clear_objects}")
        
        # 第二次可视化
        print("第二次可视化...")
        visualizer.visualize_overview(all_entities=[], processor=mock_processor)
        second_objects = len(visualizer.ax_overview.get_children())
        print(f"第二次可视化后，概览图对象数量: {second_objects}")
        
        if first_objects > 0 and second_objects > 0:
            print("✅ 缓存清除测试通过：概览图能够正确重新绘制")
        else:
            print("❌ 缓存清除测试失败：概览图没有正确绘制")
            
    except Exception as e:
        print(f"❌ 缓存清除测试失败: {e}")

if __name__ == "__main__":
    print("🎨 概览图缓存修复测试程序")
    print("=" * 50)
    
    # 运行缓存清除测试
    test_cache_clearing_only()
    
    print("\n" + "=" * 50)
    print("🖼️ 启动可视化测试...")
    
    # 运行可视化测试
    test_overview_cache_fix()
