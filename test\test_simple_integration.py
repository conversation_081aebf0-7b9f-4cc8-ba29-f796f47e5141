#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的集成测试
验证独立程序的基本功能
"""

def test_basic_import():
    """测试基本导入"""
    print("🔍 测试基本导入")
    try:
        from main_enhanced_standalone import StandaloneCADApp, StandaloneCADProcessor
        print("✅ 基本导入成功")
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False

def test_class_creation():
    """测试类创建"""
    print("🔍 测试类创建")
    try:
        from main_enhanced_standalone import StandaloneCADProcessor
        processor = StandaloneCADProcessor()
        print("✅ 处理器创建成功")
        
        # 检查基本属性
        attrs = ['current_file_entities', 'all_groups', 'labeled_entities']
        for attr in attrs:
            if hasattr(processor, attr):
                print(f"✅ 属性 {attr}: 存在")
            else:
                print(f"❌ 属性 {attr}: 缺失")
        
        return True
    except Exception as e:
        print(f"❌ 类创建失败: {e}")
        return False

def test_v2_methods():
    """测试V2方法存在性"""
    print("🔍 测试V2方法存在性")
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp
        
        # 创建临时窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        app = StandaloneCADApp(root)
        
        # 检查V2方法
        v2_methods = [
            'auto_fill_walls',
            'save_wall_fills',
            '_identify_wall_groups',
            '_basic_wall_fill'
        ]
        
        for method in v2_methods:
            if hasattr(app, method):
                print(f"✅ V2方法 {method}: 存在")
            else:
                print(f"❌ V2方法 {method}: 缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ V2方法测试失败: {e}")
        return False

def test_basic_wall_fill():
    """测试基础墙体填充"""
    print("🔍 测试基础墙体填充")
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp
        
        root = tk.Tk()
        root.withdraw()
        
        app = StandaloneCADApp(root)
        
        # 创建测试墙体
        test_wall = [
            {
                'id': 1,
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(0, 0), (100, 0)]
            },
            {
                'id': 2,
                'type': 'LINE',
                'layer': 'A-WALL',
                'points': [(100, 0), (100, 100)]
            }
        ]
        
        # 测试基础填充
        result = app._basic_wall_fill(test_wall)
        
        if result and result.get('fill_polygons'):
            print(f"✅ 基础墙体填充成功: {len(result['fill_polygons'])} 个多边形")
        else:
            print("❌ 基础墙体填充失败")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 基础墙体填充测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动简化集成测试")
    print("="*40)
    
    tests = [
        ("基本导入", test_basic_import),
        ("类创建", test_class_creation),
        ("V2方法", test_v2_methods),
        ("基础墙体填充", test_basic_wall_fill)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        result = test_func()
        results.append(result)
    
    print(f"\n" + "="*40)
    print("📊 测试结果")
    print("="*40)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ 通过" if results[i] else "❌ 失败"
        print(f"{test_name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"\n🎉 所有测试通过！({success_count}/{total_count})")
        print("✅ 独立程序V2集成成功")
    else:
        print(f"\n⚠️ 部分测试失败 ({success_count}/{total_count})")

if __name__ == "__main__":
    main()
