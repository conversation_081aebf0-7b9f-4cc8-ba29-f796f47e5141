#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试清理后的批量概览输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_clean_batch_overview():
    """测试清理后的批量概览输出"""
    
    print("🔍 测试清理后的批量概览输出")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建简单的测试数据
        all_groups = []
        groups_info = []
        all_entities = []
        
        # 创建几个测试组
        for group_id in range(3):
            group_entities = []
            for j in range(2):
                entity = {
                    'type': 'LINE',
                    'layer': f'LAYER_{group_id}',
                    'points': [[group_id*100+j*10, 0], [group_id*100+j*10+5, 0]],
                    'id': f'entity_group{group_id}_{j}'
                }
                group_entities.append(entity)
                all_entities.append(entity)
            
            all_groups.append(group_entities)
            groups_info.append({'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False})
        
        print(f"✅ 创建了{len(all_groups)}个组，总计{len(all_entities)}个实体")
        
        # 模拟配色方案和可视化器
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#87CEEB',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
                self.ax_overview = MockAxes()
        
        class MockAxes:
            def clear(self): pass
            def set_aspect(self, aspect): pass
            def grid(self, *args, **kwargs): pass
            def plot(self, *args, **kwargs): pass
            def set_title(self, title, **kwargs): pass
        
        visualizer = MockVisualizer()
        
        # 模拟处理器
        class MockProcessor:
            def __init__(self):
                self.all_groups = all_groups
                self.groups_info = groups_info
        
        processor = MockProcessor()
        
        print("\n🧪 运行批量概览（应该没有调试信息）:")
        print("=" * 50)
        
        # 运行批量概览
        result = fix._batch_overview(
            visualizer, all_entities, [], [], processor, None
        )
        
        print("=" * 50)
        print("📋 预期结果:")
        print("  ✅ 应该只显示 '✅ 批量概览完成'")
        print("  ❌ 不应该有任何调试信息：")
        print("    - 不显示组详情")
        print("    - 不显示配色方案")
        print("    - 不显示实体总数")
        print("    - 不显示组匹配统计")
        print("    - 不显示实体颜色信息")
        print("    - 不显示优先级判断")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_color_function():
    """测试实体颜色获取函数（应该无输出）"""
    
    print("\n🔧 测试实体颜色获取函数")
    print("=" * 30)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体和组
        entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'test_entity'
        }
        
        all_groups = [[entity]]
        groups_info = [{'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}]
        
        color_scheme = {
            'wall': '#8B4513',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
        
        visualizer = MockVisualizer()
        
        print("🧪 调用实体颜色获取函数（应该无输出）:")
        print("-" * 40)
        
        # 调用颜色获取函数
        color = fix._get_entity_color_for_batch_enhanced(
            entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        
        print("-" * 40)
        print(f"📋 函数返回颜色: {color}")
        print("✅ 如果上面没有调试信息，说明清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def provide_cleanup_summary():
    """提供清理总结"""
    
    print("\n📋 调试信息清理总结")
    print("=" * 30)
    
    print("✅ 已删除的调试信息:")
    print("  1. **批量概览详细调试信息** - 组数量、组详情等")
    print("  2. **配色方案输出** - 不再显示配色方案键列表")
    print("  3. **实体总数输出** - 不再显示实体总数")
    print("  4. **组匹配统计** - 不再显示匹配统计信息")
    print("  5. **实体颜色调试** - 不再显示每个实体的颜色获取过程")
    print("  6. **优先级判断输出** - 不再显示优先级判断过程")
    print("  7. **组信息输出** - 不再显示组状态和标签信息")
    print()
    print("✅ 保留的必要信息:")
    print("  1. **批量概览完成提示** - '✅ 批量概览完成'")
    print("  2. **错误信息** - 如果出现错误，仍会显示")
    print("  3. **标题信息** - 图表标题仍会显示")
    print()
    print("🎯 清理效果:")
    print("  - 批量概览运行时几乎无输出")
    print("  - 只在完成时显示简单提示")
    print("  - 大大减少了控制台刷屏")
    print("  - 提升了用户体验")

if __name__ == "__main__":
    print("🔍 批量概览输出清理测试程序")
    print("=" * 50)
    
    # 测试批量概览清理效果
    batch_result = test_clean_batch_overview()
    
    # 测试实体颜色函数清理效果
    color_result = test_entity_color_function()
    
    # 提供清理总结
    provide_cleanup_summary()
    
    print("\n" + "=" * 50)
    print("🎉 清理测试总结:")
    
    if batch_result and color_result:
        print("✅ 批量概览输出清理成功")
        print("✅ 实体颜色函数输出清理成功")
        print("✅ 所有调试信息已删除")
        print("✅ 保留了必要的功能")
        print("\n🚀 现在批量概览将:")
        print("  - 静默运行，几乎无输出")
        print("  - 只在完成时显示简单提示")
        print("  - 不再有大量调试信息刷屏")
        print("  - 提供更好的用户体验")
    else:
        print("❌ 部分清理测试失败")
        if not batch_result:
            print("❌ 批量概览输出清理失败")
        if not color_result:
            print("❌ 实体颜色函数输出清理失败")
    
    print("\n💡 使用建议:")
    print("  现在批量概览运行时将非常安静")
    print("  如果需要调试信息，可以临时添加特定的输出")
    print("  正常使用时享受简洁的界面体验")
