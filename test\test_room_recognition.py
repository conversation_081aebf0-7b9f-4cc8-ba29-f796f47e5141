#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别模块测试脚本
测试房间识别处理器和UI模块的功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_room_recognition_processor():
    """测试房间识别处理器"""
    print("=" * 80)
    print("🏠 测试房间识别处理器")
    print("=" * 80)
    
    try:
        from room_recognition_processor import RoomRecognitionProcessor
        
        # 创建处理器实例
        processor = RoomRecognitionProcessor()
        print("✅ 房间识别处理器创建成功")
        
        # 测试房间类型
        print(f"✅ 房间类型: {processor.room_types}")
        print(f"✅ 房间颜色: {list(processor.room_colors.keys())}")
        
        # 创建测试数据
        test_wall_groups = [
            [
                {
                    'type': 'LINE',
                    'start': {'x': 0, 'y': 0},
                    'end': {'x': 100, 'y': 0}
                },
                {
                    'type': 'LINE',
                    'start': {'x': 100, 'y': 0},
                    'end': {'x': 100, 'y': 100}
                },
                {
                    'type': 'LINE',
                    'start': {'x': 100, 'y': 100},
                    'end': {'x': 0, 'y': 100}
                },
                {
                    'type': 'LINE',
                    'start': {'x': 0, 'y': 100},
                    'end': {'x': 0, 'y': 0}
                }
            ]
        ]
        
        test_door_window_groups = [
            [
                {
                    'type': 'LINE',
                    'start': {'x': 40, 'y': 0},
                    'end': {'x': 60, 'y': 0}
                }
            ]
        ]
        
        # 测试房间识别
        result = processor.process_room_recognition(test_wall_groups, test_door_window_groups)
        
        if result:
            print("✅ 房间识别处理成功")
            print(f"   建筑外轮廓: {'已识别' if result['building_outline'] else '未识别'}")
            print(f"   房间数量: {len(result['rooms'])}")
            
            # 测试房间统计
            stats = processor.get_room_statistics()
            print(f"✅ 房间统计: {stats}")
            
            # 测试房间类型更新
            if processor.rooms:
                success = processor.update_room_type(0, '客厅')
                print(f"✅ 房间类型更新: {'成功' if success else '失败'}")
            
            # 测试数据导出
            export_data = processor.export_room_data()
            if export_data:
                print(f"✅ 数据导出成功，总房间数: {export_data['total_rooms']}")
            
        else:
            print("❌ 房间识别处理失败")
            return False
        
        print("✅ 房间识别处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 房间识别处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_room_recognition_ui():
    """测试房间识别UI"""
    print("\n" + "=" * 80)
    print("🖼️ 测试房间识别UI")
    print("=" * 80)
    
    try:
        from room_recognition_processor import RoomRecognitionProcessor
        from room_recognition_ui import RoomRecognitionUI
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("房间识别UI测试")
        root.geometry("800x600")
        
        # 创建处理器
        processor = RoomRecognitionProcessor()
        
        # 创建主框架
        main_frame = tk.Frame(root)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建房间识别UI
        room_ui = RoomRecognitionUI(main_frame, processor)
        
        print("✅ 房间识别UI创建成功")
        
        # 添加测试按钮
        test_frame = tk.Frame(root)
        test_frame.pack(fill='x', padx=10, pady=5)
        
        def test_with_data():
            """使用测试数据"""
            test_wall_groups = [
                [
                    {
                        'type': 'LINE',
                        'start': {'x': 0, 'y': 0},
                        'end': {'x': 200, 'y': 0}
                    },
                    {
                        'type': 'LINE',
                        'start': {'x': 200, 'y': 0},
                        'end': {'x': 200, 'y': 150}
                    },
                    {
                        'type': 'LINE',
                        'start': {'x': 200, 'y': 150},
                        'end': {'x': 0, 'y': 150}
                    },
                    {
                        'type': 'LINE',
                        'start': {'x': 0, 'y': 150},
                        'end': {'x': 0, 'y': 0}
                    }
                ]
            ]
            
            test_door_window_groups = [
                [
                    {
                        'type': 'LINE',
                        'start': {'x': 80, 'y': 0},
                        'end': {'x': 120, 'y': 0}
                    }
                ]
            ]
            
            room_ui.update_with_new_data(test_wall_groups, test_door_window_groups)
            messagebox.showinfo("测试", "测试数据已加载")
        
        test_btn = tk.Button(test_frame, text="加载测试数据", command=test_with_data,
                           bg='#E6F3FF', font=('Arial', 10))
        test_btn.pack(side='left', padx=5)
        
        def close_test():
            """关闭测试"""
            root.destroy()
        
        close_btn = tk.Button(test_frame, text="关闭测试", command=close_test,
                            bg='#FFE4E1', font=('Arial', 10))
        close_btn.pack(side='right', padx=5)
        
        # 显示说明
        info_label = tk.Label(test_frame, 
                            text="点击'加载测试数据'按钮测试房间识别功能",
                            font=('Arial', 9), fg='blue')
        info_label.pack(pady=5)
        
        print("✅ 房间识别UI测试界面准备完成")
        print("   请在弹出的窗口中测试房间识别功能")
        
        # 运行测试界面
        root.mainloop()
        
        print("✅ 房间识别UI测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 房间识别UI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n" + "=" * 80)
    print("🔗 测试集成功能")
    print("=" * 80)
    
    try:
        # 测试主应用中的房间识别模块导入
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 主应用导入成功")
        
        # 检查房间识别相关属性
        import inspect
        methods = [method for method in dir(EnhancedCADAppV2) 
                  if 'room' in method.lower() and not method.startswith('_')]
        
        if methods:
            print(f"✅ 找到房间相关方法: {methods}")
        
        # 检查房间识别相关的私有方法
        private_methods = [method for method in dir(EnhancedCADAppV2) 
                          if 'room' in method.lower() and method.startswith('_')]
        
        if private_methods:
            print(f"✅ 找到房间相关私有方法: {private_methods}")
        
        print("✅ 集成功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 集成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始房间识别模块测试...")
    
    # 执行所有测试
    tests = [
        ("房间识别处理器", test_room_recognition_processor),
        ("集成功能", test_integration),
        ("房间识别UI", test_room_recognition_ui)  # UI测试放在最后，因为会阻塞
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试通过！房间识别模块已成功集成。")
        print("\n📝 功能特性:")
        print("1. ✅ 建筑外轮廓识别：基于墙体组识别建筑外轮廓")
        print("2. ✅ 房间识别：通过墙体和门窗组识别房间区域")
        print("3. ✅ 房间分类：支持8种房间类型的自动和手动分类")
        print("4. ✅ 房间切分：支持基于门窗的房间切分功能")
        print("5. ✅ 类型修改：支持房间类型的动态修改")
        print("6. ✅ 可视化显示：房间布局图和颜色编码")
        print("7. ✅ 统计功能：房间数量和面积统计")
        print("8. ✅ 数据导出：房间信息的结构化导出")
    else:
        print("❌ 部分测试失败，请检查实现。")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
