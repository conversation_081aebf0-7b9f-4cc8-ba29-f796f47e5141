#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
房间识别处理器优化测试脚本
测试优化后的房间识别功能
"""

import sys
import os
import time
import numpy as np
from shapely.geometry import LineString, Point

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from room_recognition_processor import RoomRecognitionProcessor
    print("✅ 成功导入优化后的房间识别处理器")
except ImportError as e:
    print(f"❌ 导入房间识别处理器失败: {e}")
    sys.exit(1)

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    # 创建测试墙体组
    wall_groups = [
        [
            # 外墙
            {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 1000, 'y': 0}},
            {'type': 'LINE', 'start': {'x': 1000, 'y': 0}, 'end': {'x': 1000, 'y': 800}},
            {'type': 'LINE', 'start': {'x': 1000, 'y': 800}, 'end': {'x': 0, 'y': 800}},
            {'type': 'LINE', 'start': {'x': 0, 'y': 800}, 'end': {'x': 0, 'y': 0}},
            # 内墙
            {'type': 'LINE', 'start': {'x': 500, 'y': 0}, 'end': {'x': 500, 'y': 400}},
            {'type': 'LINE', 'start': {'x': 500, 'y': 600}, 'end': {'x': 500, 'y': 800}},
            {'type': 'LINE', 'start': {'x': 0, 'y': 400}, 'end': {'x': 300, 'y': 400}},
            {'type': 'LINE', 'start': {'x': 700, 'y': 400}, 'end': {'x': 1000, 'y': 400}},
        ]
    ]
    
    # 创建测试门窗组
    door_window_groups = [
        [
            # 门
            {'type': 'LINE', 'start': {'x': 500, 'y': 400}, 'end': {'x': 500, 'y': 600}},
        ],
        [
            # 门
            {'type': 'LINE', 'start': {'x': 300, 'y': 400}, 'end': {'x': 700, 'y': 400}},
        ]
    ]
    
    return wall_groups, door_window_groups

def test_line_extraction():
    """测试线段提取优化"""
    print("\n🧪 测试线段提取优化...")
    
    processor = RoomRecognitionProcessor()
    
    # 测试不同格式的实体
    test_entities = [
        # 标准结构
        {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 100, 'y': 100}},
        # 扁平化结构
        {'type': 'LINE', 'start_x': 0, 'start_y': 0, 'end_x': 100, 'end_y': 100},
        # 多段线
        {'type': 'LWPOLYLINE', 'points': [{'x': 0, 'y': 0}, {'x': 50, 'y': 50}, {'x': 100, 'y': 100}]},
        # 几何对象
        {'type': 'LINE', 'geometry': {'coordinates': [(0, 0), (100, 100)]}},
    ]
    
    for i, entity in enumerate(test_entities):
        coords = processor._extract_line_coordinates(entity)
        if coords:
            print(f"  ✅ 实体 {i+1}: 提取到 {len(coords)} 条线段")
        else:
            print(f"  ❌ 实体 {i+1}: 提取失败")

def test_spatial_index():
    """测试空间索引功能"""
    print("\n🧪 测试空间索引功能...")
    
    processor = RoomRecognitionProcessor()
    wall_groups, door_window_groups = create_test_data()
    
    processor.wall_groups = wall_groups
    processor.door_window_groups = door_window_groups
    
    # 构建空间索引
    start_time = time.time()
    processor._build_spatial_index()
    build_time = time.time() - start_time
    
    print(f"  ✅ 空间索引构建完成，耗时: {build_time:.3f}秒")
    print(f"  📊 墙体线段数量: {len(processor.wall_lines)}")
    print(f"  📊 门窗线段数量: {len(processor.door_window_lines)}")
    
    if processor.spatial_index:
        print("  ✅ R树索引可用")
    else:
        print("  ⚠️ 使用简单空间查询")

def test_room_classification():
    """测试房间分类算法"""
    print("\n🧪 测试房间分类算法...")
    
    processor = RoomRecognitionProcessor()
    
    # 创建测试多边形
    from shapely.geometry import Polygon
    
    test_polygons = [
        # 小房间 (卫生间)
        Polygon([(0, 0), (150, 0), (150, 200), (0, 200)]),
        # 中等房间 (卧室)
        Polygon([(0, 0), (300, 0), (300, 400), (0, 400)]),
        # 大房间 (客厅)
        Polygon([(0, 0), (500, 0), (500, 600), (0, 600)]),
        # 狭长房间 (阳台)
        Polygon([(0, 0), (100, 0), (100, 500), (0, 500)]),
        # 很小的空间 (墙体空腔)
        Polygon([(0, 0), (30, 0), (30, 100), (0, 100)]),
    ]
    
    for i, polygon in enumerate(test_polygons):
        room = processor._classify_single_region(polygon)
        print(f"  房间 {i+1}: {room['type']}, 面积: {room['area']:.1f}, "
              f"宽度: {room['width']:.1f}, 长宽比: {room['aspect_ratio']:.2f}")

def test_parallel_processing():
    """测试并行处理功能"""
    print("\n🧪 测试并行处理功能...")
    
    processor = RoomRecognitionProcessor()
    wall_groups, door_window_groups = create_test_data()
    
    processor.wall_groups = wall_groups
    processor.door_window_groups = door_window_groups
    
    # 构建空间索引
    processor._build_spatial_index()
    
    # 测试并行处理
    start_time = time.time()
    temp_lines = processor._process_door_window_groups_parallel()
    parallel_time = time.time() - start_time
    
    print(f"  ✅ 并行处理完成，耗时: {parallel_time:.3f}秒")
    print(f"  📊 生成临时线条数量: {len(temp_lines)}")

def test_full_room_recognition():
    """测试完整房间识别流程"""
    print("\n🧪 测试完整房间识别流程...")
    
    processor = RoomRecognitionProcessor()
    wall_groups, door_window_groups = create_test_data()
    
    start_time = time.time()
    result = processor.process_room_recognition(wall_groups, door_window_groups)
    total_time = time.time() - start_time
    
    if result:
        print(f"  ✅ 房间识别完成，总耗时: {total_time:.3f}秒")
        print(f"  📊 识别房间数量: {len(result['rooms'])}")
        print(f"  📊 建筑外轮廓: {'已识别' if result['building_outline'] else '未识别'}")
        
        # 显示房间详情
        for i, room in enumerate(result['rooms']):
            print(f"    房间 {i+1}: {room['type']}, 面积: {room['area']:.1f}")
    else:
        print("  ❌ 房间识别失败")

def test_debug_visualization():
    """测试调试可视化功能"""
    print("\n🧪 测试调试可视化功能...")
    
    processor = RoomRecognitionProcessor()
    wall_groups, door_window_groups = create_test_data()
    
    # 执行房间识别
    result = processor.process_room_recognition(wall_groups, door_window_groups)
    
    if result:
        # 生成调试可视化
        debug_file = "room_debug_test.html"
        processor.debug_visualization(debug_file)
        
        if os.path.exists(debug_file):
            print(f"  ✅ 调试可视化文件已生成: {debug_file}")
            # 显示文件大小
            file_size = os.path.getsize(debug_file)
            print(f"  📊 文件大小: {file_size} 字节")
        else:
            print("  ❌ 调试可视化文件生成失败")

def test_lazy_evaluation():
    """测试Lazy Evaluation功能"""
    print("\n🧪 测试Lazy Evaluation功能...")
    
    processor = RoomRecognitionProcessor()
    wall_groups, door_window_groups = create_test_data()
    
    processor.wall_groups = wall_groups
    processor.door_window_groups = door_window_groups
    
    # 测试lazy属性
    print("  第一次访问rooms_lazy...")
    start_time = time.time()
    rooms1 = processor.rooms_lazy
    first_time = time.time() - start_time
    
    print("  第二次访问rooms_lazy...")
    start_time = time.time()
    rooms2 = processor.rooms_lazy
    second_time = time.time() - start_time
    
    print(f"  ✅ 第一次计算耗时: {first_time:.3f}秒")
    print(f"  ✅ 第二次访问耗时: {second_time:.3f}秒 (缓存)")
    print(f"  📊 性能提升: {first_time/second_time:.1f}倍" if second_time > 0 else "  📊 性能提升: 无限倍")

def main():
    """主测试函数"""
    print("🚀 开始房间识别处理器优化测试")
    print("=" * 50)
    
    try:
        # 运行各项测试
        test_line_extraction()
        test_spatial_index()
        test_room_classification()
        test_parallel_processing()
        test_full_room_recognition()
        test_debug_visualization()
        test_lazy_evaluation()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
