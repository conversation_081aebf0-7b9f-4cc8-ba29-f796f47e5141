#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试隐藏左侧和隐藏右侧功能的实际效果
检查按钮功能和界面布局变化
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_hide_panels_functionality():
    """测试隐藏左侧和隐藏右侧功能"""
    print("🔍 开始测试隐藏左侧和隐藏右侧功能...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("隐藏面板功能测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(1)
        
        # 测试1：检查按钮是否存在
        print("\n🔍 测试1：检查隐藏面板按钮是否存在")
        
        buttons_to_check = [
            ('toggle_left_button', '隐藏左侧按钮'),
            ('toggle_right_button', '隐藏右侧按钮')
        ]
        
        for button_attr, button_name in buttons_to_check:
            if hasattr(app, button_attr):
                button = getattr(app, button_attr)
                if button:
                    try:
                        text = button.cget('text')
                        state = button.cget('state')
                        print(f"  ✅ {button_name}: 文本='{text}', 状态={state}")
                    except Exception as e:
                        print(f"  ❌ {button_name}: 属性获取失败 - {e}")
                        return False
                else:
                    print(f"  ❌ {button_name}: 对象为空")
                    return False
            else:
                print(f"  ❌ {button_name}: 属性不存在")
                return False
        
        # 测试2：检查初始界面布局
        print("\n🔍 测试2：检查初始界面布局")
        
        # 检查左侧面板
        if hasattr(app, 'detail_frame'):
            detail_visible = app.detail_frame.winfo_viewable()
            detail_mapped = app.detail_frame.winfo_ismapped()
            print(f"  左侧详细面板: 可见={detail_visible}, 映射={detail_mapped}")
            
            if detail_visible and detail_mapped:
                detail_width = app.detail_frame.winfo_width()
                detail_height = app.detail_frame.winfo_height()
                print(f"    尺寸: 宽度={detail_width}, 高度={detail_height}")
            else:
                print(f"    ⚠️ 左侧详细面板不可见")
        else:
            print(f"  ❌ 左侧详细面板不存在")
        
        # 检查概览面板
        if hasattr(app, 'overview_frame'):
            overview_visible = app.overview_frame.winfo_viewable()
            overview_mapped = app.overview_frame.winfo_ismapped()
            print(f"  概览面板: 可见={overview_visible}, 映射={overview_mapped}")
            
            if overview_visible and overview_mapped:
                overview_width = app.overview_frame.winfo_width()
                overview_height = app.overview_frame.winfo_height()
                overview_grid_info = app.overview_frame.grid_info()
                print(f"    尺寸: 宽度={overview_width}, 高度={overview_height}")
                print(f"    网格信息: {overview_grid_info}")
            else:
                print(f"    ⚠️ 概览面板不可见")
        else:
            print(f"  ❌ 概览面板不存在")
        
        # 检查右侧面板（如果存在）
        if hasattr(app, 'right_panel'):
            right_visible = app.right_panel.winfo_viewable()
            right_mapped = app.right_panel.winfo_ismapped()
            print(f"  右侧面板: 可见={right_visible}, 映射={right_mapped}")
        else:
            print(f"  右侧面板不存在（这是正常的）")
        
        # 测试3：测试隐藏左侧功能
        print("\n🔍 测试3：测试隐藏左侧功能")
        
        try:
            # 记录初始状态
            initial_left_text = app.toggle_left_button.cget('text')
            print(f"  初始按钮文本: '{initial_left_text}'")
            
            # 记录初始布局
            if hasattr(app, 'detail_frame') and hasattr(app, 'overview_frame'):
                initial_detail_visible = app.detail_frame.winfo_viewable()
                initial_overview_grid = app.overview_frame.grid_info()
                print(f"  初始状态: 左侧面板可见={initial_detail_visible}")
                print(f"  初始概览面板网格: column={initial_overview_grid.get('column')}, columnspan={initial_overview_grid.get('columnspan')}")
            
            # 点击隐藏左侧按钮
            print(f"  点击隐藏左侧按钮...")
            app._toggle_left_panel()
            root.update()
            time.sleep(0.5)
            
            # 检查变化
            after_left_text = app.toggle_left_button.cget('text')
            print(f"  点击后按钮文本: '{after_left_text}'")
            
            if hasattr(app, 'detail_frame') and hasattr(app, 'overview_frame'):
                after_detail_visible = app.detail_frame.winfo_viewable()
                after_overview_grid = app.overview_frame.grid_info()
                print(f"  点击后状态: 左侧面板可见={after_detail_visible}")
                print(f"  点击后概览面板网格: column={after_overview_grid.get('column')}, columnspan={after_overview_grid.get('columnspan')}")
                
                # 验证功能是否正确
                if initial_left_text != after_left_text:
                    print(f"  ✅ 按钮文本正确变化")
                else:
                    print(f"  ❌ 按钮文本未变化")
                    return False
                
                if initial_detail_visible != after_detail_visible:
                    print(f"  ✅ 左侧面板可见性正确变化")
                else:
                    print(f"  ❌ 左侧面板可见性未变化")
                    return False
                
                # 检查概览面板是否扩展
                initial_columnspan = initial_overview_grid.get('columnspan', 1)
                after_columnspan = after_overview_grid.get('columnspan', 1)
                if after_columnspan > initial_columnspan:
                    print(f"  ✅ 概览面板正确扩展 (columnspan: {initial_columnspan} -> {after_columnspan})")
                else:
                    print(f"  ⚠️ 概览面板可能未正确扩展 (columnspan: {initial_columnspan} -> {after_columnspan})")
            
            # 再次点击恢复
            print(f"  再次点击恢复...")
            app._toggle_left_panel()
            root.update()
            time.sleep(0.5)
            
            # 检查恢复状态
            restored_left_text = app.toggle_left_button.cget('text')
            print(f"  恢复后按钮文本: '{restored_left_text}'")
            
            if restored_left_text == initial_left_text:
                print(f"  ✅ 左侧面板功能测试通过")
            else:
                print(f"  ❌ 左侧面板恢复有问题")
                return False
                
        except Exception as e:
            print(f"  ❌ 隐藏左侧功能测试失败: {e}")
            return False
        
        # 测试4：测试隐藏右侧功能
        print("\n🔍 测试4：测试隐藏右侧功能")
        
        try:
            # 记录初始状态
            initial_right_text = app.toggle_right_button.cget('text')
            print(f"  初始按钮文本: '{initial_right_text}'")
            
            # 点击隐藏右侧按钮
            print(f"  点击隐藏右侧按钮...")
            app._toggle_right_panel()
            root.update()
            time.sleep(0.5)
            
            # 检查变化
            after_right_text = app.toggle_right_button.cget('text')
            print(f"  点击后按钮文本: '{after_right_text}'")
            
            # 验证功能是否正确
            if initial_right_text != after_right_text:
                print(f"  ✅ 按钮文本正确变化")
            else:
                print(f"  ❌ 按钮文本未变化")
                return False
            
            # 再次点击恢复
            print(f"  再次点击恢复...")
            app._toggle_right_panel()
            root.update()
            time.sleep(0.5)
            
            # 检查恢复状态
            restored_right_text = app.toggle_right_button.cget('text')
            print(f"  恢复后按钮文本: '{restored_right_text}'")
            
            if restored_right_text == initial_right_text:
                print(f"  ✅ 右侧面板功能测试通过")
            else:
                print(f"  ❌ 右侧面板恢复有问题")
                return False
                
        except Exception as e:
            print(f"  ❌ 隐藏右侧功能测试失败: {e}")
            return False
        
        # 测试5：测试组合使用效果
        print("\n🔍 测试5：测试组合使用效果")
        
        try:
            print(f"  测试同时隐藏左侧和右侧...")
            
            # 隐藏左侧
            app._toggle_left_panel()
            root.update()
            time.sleep(0.3)
            
            # 隐藏右侧
            app._toggle_right_panel()
            root.update()
            time.sleep(0.3)
            
            # 检查状态
            left_text = app.toggle_left_button.cget('text')
            right_text = app.toggle_right_button.cget('text')
            print(f"  同时隐藏后: 左侧='{left_text}', 右侧='{right_text}'")
            
            # 检查概览面板是否最大化
            if hasattr(app, 'overview_frame'):
                max_overview_grid = app.overview_frame.grid_info()
                max_columnspan = max_overview_grid.get('columnspan', 1)
                print(f"  概览面板最大化状态: columnspan={max_columnspan}")
                
                if max_columnspan >= 2:
                    print(f"  ✅ 概览面板正确最大化")
                else:
                    print(f"  ⚠️ 概览面板可能未完全最大化")
            
            # 恢复所有面板
            print(f"  恢复所有面板...")
            app._toggle_left_panel()
            root.update()
            time.sleep(0.3)
            
            app._toggle_right_panel()
            root.update()
            time.sleep(0.3)
            
            # 检查恢复状态
            final_left_text = app.toggle_left_button.cget('text')
            final_right_text = app.toggle_right_button.cget('text')
            print(f"  恢复后: 左侧='{final_left_text}', 右侧='{final_right_text}'")
            
            print(f"  ✅ 组合使用测试通过")
                
        except Exception as e:
            print(f"  ❌ 组合使用测试失败: {e}")
            return False
        
        # 测试6：视觉确认测试
        print("\n🔍 测试6：视觉确认测试")
        print("  请观察界面变化，程序将依次演示各种隐藏效果...")
        print("  1. 正常状态 (3秒)")
        root.update()
        time.sleep(3)
        
        print("  2. 隐藏左侧 (3秒)")
        app._toggle_left_panel()
        root.update()
        time.sleep(3)
        
        print("  3. 恢复左侧，隐藏右侧 (3秒)")
        app._toggle_left_panel()
        app._toggle_right_panel()
        root.update()
        time.sleep(3)
        
        print("  4. 同时隐藏左侧和右侧 (3秒)")
        app._toggle_left_panel()
        root.update()
        time.sleep(3)
        
        print("  5. 恢复所有面板 (2秒)")
        app._toggle_left_panel()
        app._toggle_right_panel()
        root.update()
        time.sleep(2)
        
        print("  ✅ 视觉确认测试完成")
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 隐藏面板功能测试完全通过！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_hide_panels_functionality()
    if success:
        print("\n🎊 隐藏面板功能测试成功！")
        print("✅ 隐藏左侧功能正常工作")
        print("✅ 隐藏右侧功能正常工作")
        print("✅ 界面布局正确响应")
        print("✅ 按钮状态正确更新")
    else:
        print("\n⚠️ 隐藏面板功能测试失败")
        print("❌ 需要检查功能实现")
