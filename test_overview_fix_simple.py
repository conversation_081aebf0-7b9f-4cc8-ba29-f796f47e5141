#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试概览图修复
验证概览图是否使用基于组信息的颜色
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_overview_fix():
    """简单测试概览图修复"""
    print("🔍 开始简单测试概览图修复...")
    
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 测试1：检查关键方法是否存在
        print("\n🔍 测试1：检查关键方法是否存在")
        
        required_methods = [
            '_get_safe_color',
            '_visualize_overview_with_group_colors',
            '_draw_entities_simplified_group_based',
            '_get_group_color_for_overview'
        ]
        
        for method_name in required_methods:
            if hasattr(EnhancedCADAppV2, method_name):
                print(f"    ✅ {method_name} - 存在")
            else:
                print(f"    ❌ {method_name} - 缺失")
        
        # 测试2：检查_get_safe_color方法的实现
        print("\n🔍 测试2：检查_get_safe_color方法的实现")
        
        # 创建一个临时实例来测试方法
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        try:
            app = EnhancedCADAppV2(root)
            
            # 测试_get_safe_color方法
            if hasattr(app, '_get_safe_color'):
                # 测试没有配色方案的情况
                result = app._get_safe_color('other')
                print(f"    _get_safe_color('other') 返回: {result}")
                
                # 检查是否有配色方案
                if hasattr(app, 'current_color_scheme') and app.current_color_scheme:
                    print(f"    配色方案存在: {len(app.current_color_scheme)} 种颜色")
                    
                    # 测试获取具体颜色
                    test_keys = ['other', 'unlabeled', 'current_group', 'wall']
                    for key in test_keys:
                        color = app._get_safe_color(key)
                        print(f"      {key}: {color}")
                else:
                    print(f"    ❌ 配色方案不存在")
            
            # 测试替代视图器
            if hasattr(app, 'alternative_visualizer') and app.alternative_visualizer:
                print(f"    替代视图器存在: {type(app.alternative_visualizer)}")
                
                if hasattr(app.alternative_visualizer, '_get_color_from_scheme'):
                    alt_color = app.alternative_visualizer._get_color_from_scheme('other')
                    print(f"    替代视图器颜色('other'): {alt_color}")
            
        except Exception as e:
            print(f"    ❌ 应用实例创建失败: {e}")
        finally:
            root.destroy()
        
        # 测试3：检查概览图方法的调用路径
        print("\n🔍 测试3：检查概览图方法的调用路径")
        
        # 检查visualize_overview方法的实现
        import inspect
        
        try:
            viz_method = getattr(EnhancedCADAppV2, 'visualize_overview')
            source_lines = inspect.getsourcelines(viz_method)
            
            print(f"    visualize_overview 方法行数: {len(source_lines[0])}")
            print(f"    起始行号: {source_lines[1]}")
            
            # 检查是否调用了正确的方法
            source_code = ''.join(source_lines[0])
            if '_visualize_overview_with_group_colors' in source_code:
                print(f"    ✅ 调用了 _visualize_overview_with_group_colors")
            else:
                print(f"    ❌ 没有调用 _visualize_overview_with_group_colors")
            
            if 'super().visualize_overview' in source_code:
                print(f"    ⚠️ 仍然调用了父类方法")
            else:
                print(f"    ✅ 没有调用父类方法")
                
        except Exception as e:
            print(f"    ❌ 检查方法实现失败: {e}")
        
        # 测试4：检查_draw_entities_simplified_group_based的修复
        print("\n🔍 测试4：检查_draw_entities_simplified_group_based的修复")
        
        try:
            method = getattr(EnhancedCADAppV2, '_draw_entities_simplified_group_based')
            source_lines = inspect.getsourcelines(method)
            source_code = ''.join(source_lines[0])
            
            # 检查是否有正确的None处理
            if 'if default_color is None:' in source_code:
                print(f"    ✅ 有正确的None处理逻辑")
            else:
                print(f"    ❌ 缺少None处理逻辑")
            
            # 检查是否使用了替代视图器
            if 'alternative_visualizer' in source_code:
                print(f"    ✅ 使用了替代视图器作为备用")
            else:
                print(f"    ❌ 没有使用替代视图器作为备用")
                
        except Exception as e:
            print(f"    ❌ 检查方法修复失败: {e}")
        
        print(f"\n🎉 简单测试概览图修复完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_overview_fix()
    if success:
        print("\n🎊 概览图修复测试成功！")
    else:
        print("\n⚠️ 概览图修复测试发现问题")
