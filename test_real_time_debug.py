#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试实时调试输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_time_debug():
    """测试实时调试输出"""
    
    print("🔍 测试批量概览实时调试输出")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 模拟真实的配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'furniture': '#4DB6AC',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'pending': '#FFA500',
            'other': '#808080'
        }
        
        # 测试不同的组信息场景
        test_scenarios = [
            {
                'name': '自动标注的墙体',
                'group_info': {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},
                'expected': '#8B4513'
            },
            {
                'name': '自动标注的门窗',
                'group_info': {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False},
                'expected': '#FFD700'
            },
            {
                'name': '当前组',
                'group_info': {'status': 'labeled', 'label': 'wall', 'is_current_group': True},
                'expected': '#FF0000'
            },
            {
                'name': '标注中状态',
                'group_info': {'status': 'labeling', 'label': '未标注', 'is_current_group': False},
                'expected': '#FF0000'
            },
            {
                'name': '未标注状态',
                'group_info': {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False},
                'expected': '#D3D3D3'
            }
        ]
        
        print("🧪 测试各种组信息场景:")
        all_passed = True
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n  测试 {i+1}: {scenario['name']}")
            print(f"  " + "-" * 40)
            
            actual_color = fix._get_color_from_group_info(scenario['group_info'], color_scheme)
            expected_color = scenario['expected']
            
            print(f"  期望颜色: {expected_color}")
            print(f"  实际颜色: {actual_color}")
            
            if actual_color == expected_color:
                print(f"  ✅ 测试通过")
            else:
                print(f"  ❌ 测试失败")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_overview_with_debug():
    """测试带调试的批量概览"""
    
    print("\n🎨 测试带调试的批量概览")
    print("=" * 40)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        test_entities = []
        for i in range(5):  # 少量实体便于观察
            entity = {
                'type': 'LINE',
                'layer': f'A-WALL-{i}',
                'points': [[i*10, 0], [(i+1)*10, 0]],
                'id': f'entity_{i}'
            }
            test_entities.append(entity)
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'door_window': '#FFD700',
                    'current_group': '#FF0000',
                    'unlabeled': '#D3D3D3',
                    'other': '#808080'
                }
                self.ax_overview = MockAxis()
        
        class MockAxis:
            def clear(self): pass
            def set_aspect(self, aspect): pass
            def grid(self, *args, **kwargs): pass
            def plot(self, *args, **kwargs): pass
            def set_title(self, *args, **kwargs): pass
        
        visualizer = MockVisualizer()
        
        # 模拟处理器
        class MockProcessor:
            def __init__(self):
                self.all_groups = [
                    test_entities[0:2],   # 组1：墙体
                    test_entities[2:4],   # 组2：门窗
                    test_entities[4:5]    # 组3：未标注
                ]
                self.groups_info = [
                    {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},
                    {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False},
                    {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}
                ]
        
        processor = MockProcessor()
        
        print("  调用批量概览...")
        print("  " + "=" * 30)
        
        # 重置调试标记
        if hasattr(fix, '_batch_color_debug_shown'):
            delattr(fix, '_batch_color_debug_shown')
        
        result = fix.optimize_visualize_overview(
            visualizer, test_entities, [], [], 
            processor=processor
        )
        
        print("  " + "=" * 30)
        print(f"  批量概览结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量概览测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_debug_summary():
    """提供调试总结"""
    
    print("\n📋 实时调试总结")
    print("=" * 25)
    
    print("🔧 已添加的调试功能:")
    print("  ✅ 详细的组信息输入输出")
    print("  ✅ 每个优先级判断的过程")
    print("  ✅ 标签到颜色的映射结果")
    print("  ✅ 最终返回颜色的确认")
    
    print("\n🎯 调试输出内容:")
    print("  - 输入组信息的完整内容")
    print("  - 解析后的status、label、is_current")
    print("  - 每个优先级分支的判断结果")
    print("  - 标签映射的详细过程")
    print("  - 最终返回的颜色值")
    
    print("\n💡 使用方法:")
    print("  1. 运行实际程序，触发批量概览")
    print("  2. 观察控制台的详细调试输出")
    print("  3. 确认每个实体的颜色获取过程")
    print("  4. 找出灰色显示的具体原因")
    
    print("\n🚀 预期效果:")
    print("  - 如果组信息正确，应该看到正确的颜色映射")
    print("  - 如果仍显示灰色，调试输出会显示具体原因")
    print("  - 可以精确定位问题所在的环节")

if __name__ == "__main__":
    print("🔍 批量概览实时调试测试程序")
    print("=" * 50)
    
    # 测试实时调试
    debug_result = test_real_time_debug()
    
    # 测试带调试的批量概览
    batch_result = test_batch_overview_with_debug()
    
    # 提供调试总结
    provide_debug_summary()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结:")
    
    if debug_result and batch_result:
        print("✅ 实时调试功能测试通过")
        print("✅ 批量概览调试输出正常")
        print("✅ 详细调试信息已添加")
        print("\n🚀 现在运行实际程序，查看详细的调试输出！")
        print("  - 每个实体的颜色获取过程都会被详细记录")
        print("  - 可以精确找出灰色显示的原因")
        print("  - 验证组信息和颜色映射是否正确")
    else:
        print("❌ 部分测试失败")
        if not debug_result:
            print("❌ 实时调试功能测试失败")
        if not batch_result:
            print("❌ 批量概览调试测试失败")
