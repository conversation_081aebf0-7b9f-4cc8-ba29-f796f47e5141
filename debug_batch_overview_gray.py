#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试批量概览灰色问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_batch_overview_call():
    """调试批量概览调用情况"""
    
    print("🔍 调试批量概览灰色问题")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 模拟真实的调用场景
        print("📋 模拟真实调用场景...")
        
        # 创建测试实体
        test_entities = []
        for i in range(455):  # 模拟455个实体
            entity = {
                'type': 'LINE',
                'layer': f'Layer_{i % 10}',
                'label': 'wall' if i % 3 == 0 else ('door_window' if i % 3 == 1 else None),
                'points': [[i*0.1, 0], [(i+1)*0.1, 0]],
                'id': f'entity_{i}'
            }
            test_entities.append(entity)
        
        print(f"  创建了 {len(test_entities)} 个测试实体")
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'door_window': '#FFD700',
                    'furniture': '#4DB6AC',
                    'current_group': '#FF0000',
                    'highlight': '#FF0000',
                    'unlabeled': '#C0C0C0',
                    'other': '#808080'
                }
                self.ax_overview = MockAxis()
            
            def _get_entity_display_info_enhanced(self, entity, **kwargs):
                # 模拟增强颜色获取方法
                label = entity.get('label')
                if label == 'wall':
                    return '#8B4513', 1.0, 1.0, 'labeled'
                elif label == 'door_window':
                    return '#FFD700', 1.0, 1.0, 'labeled'
                else:
                    return '#C0C0C0', 1.0, 1.0, 'unlabeled'
        
        class MockAxis:
            def clear(self): pass
            def set_aspect(self, aspect): pass
            def grid(self, *args, **kwargs): pass
            def plot(self, *args, **kwargs): pass
            def set_title(self, *args, **kwargs): pass
        
        visualizer = MockVisualizer()
        
        # 测试场景1：有处理器和组信息
        print("\n🧪 测试场景1：有处理器和组信息")
        
        class MockProcessorWithGroups:
            def __init__(self):
                # 模拟组信息
                self.all_groups = [
                    test_entities[0:150],   # 组1：墙体
                    test_entities[150:300], # 组2：门窗
                    test_entities[300:455]  # 组3：未标注
                ]
                self.groups_info = [
                    {'status': 'labeled', 'label': 'wall', 'is_current_group': False},
                    {'status': 'labeled', 'label': 'door_window', 'is_current_group': True},
                    {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}
                ]
        
        processor_with_groups = MockProcessorWithGroups()
        
        # 测试批量概览触发
        result1 = fix.optimize_visualize_overview(
            visualizer, test_entities, [], [], 
            processor=processor_with_groups
        )
        
        print(f"  结果: {result1}")
        print(f"  处理器有组信息: {hasattr(processor_with_groups, 'all_groups')}")
        print(f"  组数量: {len(processor_with_groups.all_groups)}")
        print(f"  组信息数量: {len(processor_with_groups.groups_info)}")
        
        # 测试场景2：无处理器
        print("\n🧪 测试场景2：无处理器")
        
        result2 = fix.optimize_visualize_overview(
            visualizer, test_entities, [], []
        )
        
        print(f"  结果: {result2}")
        print(f"  应该使用批量概览（大数据集）")
        
        # 测试场景3：有处理器但无组信息
        print("\n🧪 测试场景3：有处理器但无组信息")
        
        class MockProcessorNoGroups:
            def __init__(self):
                pass  # 没有all_groups属性
        
        processor_no_groups = MockProcessorNoGroups()
        
        result3 = fix.optimize_visualize_overview(
            visualizer, test_entities, [], [], 
            processor=processor_no_groups
        )
        
        print(f"  结果: {result3}")
        print(f"  处理器有组信息: {hasattr(processor_no_groups, 'all_groups')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_getting_logic():
    """测试颜色获取逻辑"""
    
    print("\n🎨 测试颜色获取逻辑")
    print("=" * 40)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        test_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'label': 'wall',
            'points': [[0, 0], [10, 0]]
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'door_window': '#FFD700',
                    'unlabeled': '#C0C0C0',
                    'other': '#808080'
                }
            
            def _get_entity_display_info_enhanced(self, entity, **kwargs):
                label = entity.get('label')
                if label == 'wall':
                    return '#8B4513', 1.0, 1.0, 'labeled'
                else:
                    return '#C0C0C0', 1.0, 1.0, 'unlabeled'
        
        visualizer = MockVisualizer()
        
        # 测试不同的组信息场景
        test_scenarios = [
            {
                'name': '有组信息，实体在组中',
                'all_groups': [[test_entity]],
                'groups_info': [{'status': 'labeled', 'label': 'wall', 'is_current_group': False}],
                'expected_color': '#8B4513'
            },
            {
                'name': '有组信息，当前组',
                'all_groups': [[test_entity]],
                'groups_info': [{'status': 'labeled', 'label': 'wall', 'is_current_group': True}],
                'expected_color': '#FF0000'  # 当前组颜色
            },
            {
                'name': '无组信息，有增强方法',
                'all_groups': [],
                'groups_info': [],
                'expected_color': '#8B4513'  # 通过增强方法获取
            },
            {
                'name': '无组信息，无增强方法',
                'all_groups': [],
                'groups_info': [],
                'expected_color': '#C0C0C0',  # 默认颜色
                'remove_enhanced': True
            }
        ]
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n场景 {i+1}: {scenario['name']}")
            
            # 临时移除增强方法（如果需要）
            original_method = None
            if scenario.get('remove_enhanced'):
                if hasattr(visualizer, '_get_entity_display_info_enhanced'):
                    original_method = visualizer._get_entity_display_info_enhanced
                    delattr(visualizer, '_get_entity_display_info_enhanced')
            
            try:
                color = fix._get_entity_color_for_batch_enhanced(
                    test_entity,
                    visualizer.color_scheme,
                    visualizer,
                    [],
                    scenario['all_groups'],
                    scenario['groups_info'],
                    None
                )
                
                print(f"  获取颜色: {color}")
                print(f"  期望颜色: {scenario['expected_color']}")
                
                if color == scenario['expected_color']:
                    print(f"  ✅ 颜色正确")
                else:
                    print(f"  ❌ 颜色错误")
                
            finally:
                # 恢复增强方法
                if original_method:
                    visualizer._get_entity_display_info_enhanced = original_method
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色获取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_gray_color_cause():
    """分析灰色显示的可能原因"""
    
    print("\n🔍 分析灰色显示的可能原因")
    print("=" * 40)
    
    print("🎯 可能的原因分析:")
    print()
    print("1. **处理器参数传递问题**")
    print("   - 实际调用时processor参数为None")
    print("   - 处理器没有all_groups或groups_info属性")
    print("   - 组信息数据为空或格式不正确")
    print()
    print("2. **批量概览条件判断问题**")
    print("   - 455个实体触发了批量概览")
    print("   - 但没有组信息，导致使用默认颜色")
    print("   - 增强颜色获取方法失效")
    print()
    print("3. **颜色获取逻辑问题**")
    print("   - _get_entity_color_for_batch_enhanced方法有bug")
    print("   - 组信息匹配失败")
    print("   - 最终回退到默认的灰色")
    print()
    print("4. **配色方案问题**")
    print("   - color_scheme中缺少必要的颜色定义")
    print("   - unlabeled颜色被设置为灰色")
    print("   - 实体标签与配色方案不匹配")
    print()
    print("💡 解决方案建议:")
    print()
    print("1. **强制调试输出**")
    print("   - 在批量概览中添加详细的调试信息")
    print("   - 输出处理器状态、组信息、颜色获取过程")
    print()
    print("2. **改进回退机制**")
    print("   - 当组信息缺失时，使用实体标签直接获取颜色")
    print("   - 确保有多层颜色获取回退")
    print()
    print("3. **验证实际调用**")
    print("   - 在实际使用中添加日志输出")
    print("   - 确认处理器参数是否正确传递")

if __name__ == "__main__":
    print("🔍 批量概览灰色问题调试程序")
    print("=" * 50)
    
    # 调试批量概览调用
    debug_result = debug_batch_overview_call()
    
    # 测试颜色获取逻辑
    color_result = test_color_getting_logic()
    
    # 分析可能原因
    analyze_gray_color_cause()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结:")
    
    if debug_result and color_result:
        print("✅ 批量概览逻辑测试通过")
        print("✅ 颜色获取逻辑正常")
        print("⚠️ 问题可能在实际调用时的参数传递")
        print("\n💡 建议：")
        print("  1. 检查实际使用时的处理器参数")
        print("  2. 添加更多调试输出到批量概览方法")
        print("  3. 验证组信息的完整性")
    else:
        print("❌ 部分测试失败")
        if not debug_result:
            print("❌ 批量概览调用测试失败")
        if not color_result:
            print("❌ 颜色获取逻辑测试失败")
