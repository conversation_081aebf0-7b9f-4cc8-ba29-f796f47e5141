#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断alternative_visualizer的问题
检查概览图是否正确使用了替代视图的颜色逻辑
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_alternative_visualizer():
    """诊断alternative_visualizer的问题"""
    print("🔍 开始诊断alternative_visualizer问题...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 诊断1：检查alternative_visualizer是否正确初始化
        print("\n🔍 诊断1：检查alternative_visualizer是否正确初始化")
        
        if hasattr(app, 'alternative_visualizer'):
            if app.alternative_visualizer:
                print("  ✅ alternative_visualizer 存在且不为空")
                
                # 检查关键方法
                if hasattr(app.alternative_visualizer, 'get_alternative_entity_color'):
                    print("  ✅ get_alternative_entity_color 方法存在")
                else:
                    print("  ❌ get_alternative_entity_color 方法不存在")
                    return False
                
                # 检查配色方案
                if hasattr(app.alternative_visualizer, 'color_scheme'):
                    color_scheme = app.alternative_visualizer.color_scheme
                    print(f"  配色方案包含 {len(color_scheme)} 种颜色")
                    if len(color_scheme) > 0:
                        print("  ✅ 配色方案已设置")
                    else:
                        print("  ❌ 配色方案为空")
                        return False
                else:
                    print("  ❌ 配色方案属性不存在")
                    return False
            else:
                print("  ❌ alternative_visualizer 为空")
                return False
        else:
            print("  ❌ alternative_visualizer 属性不存在")
            return False
        
        # 诊断2：测试alternative_visualizer的颜色获取
        print("\n🔍 诊断2：测试alternative_visualizer的颜色获取")
        
        # 创建测试组信息
        test_groups_info = [
            {
                'status': 'labeled',
                'label': 'wall',
                'description': '墙体组'
            },
            {
                'status': 'auto_labeled',
                'label': 'door_window',
                'description': '门窗组'
            },
            {
                'status': 'labeled',
                'label': 'furniture',
                'description': '家具组'
            }
        ]
        
        for i, group_info in enumerate(test_groups_info):
            print(f"\n  测试组 {i+1}:")
            print(f"    组信息: {group_info}")
            
            try:
                # 直接调用alternative_visualizer的方法
                color = app.alternative_visualizer.get_alternative_entity_color(None, group_info)
                print(f"    获取的颜色: {color}")
                
                # 验证颜色是否有效
                if color and color != '#808080':
                    print(f"    ✅ 颜色有效且非默认灰色")
                else:
                    print(f"    ⚠️ 颜色可能有问题")
                    
            except Exception as e:
                print(f"    ❌ 颜色获取失败: {e}")
                return False
        
        # 诊断3：检查概览图的颜色获取方法
        print("\n🔍 诊断3：检查概览图的颜色获取方法")
        
        if hasattr(app, '_get_group_color_for_overview'):
            print("  ✅ _get_group_color_for_overview 方法存在")
            
            # 测试这个方法
            for i, group_info in enumerate(test_groups_info):
                print(f"\n  测试组 {i+1}:")
                
                try:
                    # 创建测试实体
                    test_entity = {
                        'id': i+1,
                        'type': 'LINE',
                        'layer': 'TEST_LAYER',
                        'label': 'wrong_label'  # 故意设置错误的实体标签
                    }
                    
                    # 调用概览图颜色获取方法
                    overview_color = app._get_group_color_for_overview(group_info, test_entity, [])
                    print(f"    概览图颜色: {overview_color}")
                    
                    # 直接调用alternative_visualizer方法作为对比
                    alt_color = app.alternative_visualizer.get_alternative_entity_color(None, group_info)
                    print(f"    替代视图颜色: {alt_color}")
                    
                    # 比较颜色
                    if overview_color == alt_color:
                        print(f"    ✅ 颜色一致，概览图正确使用了替代视图逻辑")
                    else:
                        print(f"    ❌ 颜色不一致，概览图可能有问题")
                        print(f"       期望: {alt_color}")
                        print(f"       实际: {overview_color}")
                        return False
                        
                except Exception as e:
                    print(f"    ❌ 概览图颜色获取失败: {e}")
                    return False
        else:
            print("  ❌ _get_group_color_for_overview 方法不存在")
            return False
        
        # 诊断4：检查视图切换系统
        print("\n🔍 诊断4：检查视图切换系统")
        
        if hasattr(app, 'current_view_mode'):
            print(f"  当前视图模式: {app.current_view_mode}")
            
            if hasattr(app, '_original_visualize_overview'):
                print("  ✅ 原始visualize_overview方法已保存")
            else:
                print("  ❌ 原始visualize_overview方法未保存")
            
            # 检查当前使用的visualize_overview方法
            if hasattr(app.visualizer, 'visualize_overview'):
                method_name = app.visualizer.visualize_overview.__name__
                print(f"  当前使用的visualize_overview方法: {method_name}")
                
                if method_name == '_alternative_visualize_overview':
                    print("  ✅ 正在使用替代视图方法")
                else:
                    print("  ⚠️ 可能没有使用替代视图方法")
            else:
                print("  ❌ visualize_overview方法不存在")
        else:
            print("  ❌ 视图模式信息不存在")
        
        # 诊断5：测试完整的概览图绘制流程
        print("\n🔍 诊断5：测试完整的概览图绘制流程")
        
        # 创建测试数据
        test_entities = [
            {
                'id': 1,
                'type': 'LINE',
                'layer': 'WRONG_LAYER',  # 故意设置错误的图层
                'label': 'wrong_label',  # 故意设置错误的标签
                'points': [(0, 0), (100, 0)],
                'start': (0, 0),
                'end': (100, 0)
            },
            {
                'id': 2,
                'type': 'LINE',
                'layer': 'WRONG_LAYER',  # 故意设置错误的图层
                'label': 'wrong_label',  # 故意设置错误的标签
                'points': [(100, 0), (200, 0)],
                'start': (100, 0),
                'end': (200, 0)
            }
        ]
        
        test_groups = [
            [test_entities[0]],  # 墙体组
            [test_entities[1]]   # 门窗组
        ]
        
        try:
            # 模拟处理器
            class MockProcessor:
                def __init__(self):
                    self.all_groups = test_groups
                    self.groups_info = test_groups_info[:2]  # 只使用前两个组信息
                    self.current_file_entities = test_entities
            
            mock_processor = MockProcessor()
            
            # 调用概览图的颜色获取方法
            print(f"  测试完整的概览图绘制流程:")
            
            # 测试_visualize_overview_with_group_colors方法
            if hasattr(app, '_visualize_overview_with_group_colors'):
                print(f"    调用_visualize_overview_with_group_colors...")
                app._visualize_overview_with_group_colors(
                    test_entities, [], [], mock_processor, None, None, None
                )
                
                # 检查绘制结果
                if hasattr(app.visualizer, 'ax_overview'):
                    lines = app.visualizer.ax_overview.get_lines()
                    patches = app.visualizer.ax_overview.patches
                    
                    print(f"    绘制结果: {len(lines)} 条线, {len(patches)} 个图形")
                    
                    # 检查颜色多样性
                    colors_used = set()
                    for line in lines:
                        colors_used.add(str(line.get_color()))
                    for patch in patches:
                        colors_used.add(str(patch.get_edgecolor()))
                    
                    print(f"    使用的颜色: {list(colors_used)}")
                    
                    if len(colors_used) > 1:
                        print(f"    ✅ 使用了多种颜色，概览图颜色显示正常")
                    else:
                        print(f"    ❌ 只使用了单一颜色，可能存在问题")
                        return False
                else:
                    print(f"    ❌ 概览图轴不存在")
                    return False
            else:
                print(f"    ❌ _visualize_overview_with_group_colors方法不存在")
                return False
                
        except Exception as e:
            print(f"    ❌ 完整流程测试失败: {e}")
            return False
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 alternative_visualizer诊断完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = diagnose_alternative_visualizer()
    if success:
        print("\n🎊 alternative_visualizer工作正常！")
        print("✅ 概览图应该正确使用组信息而不是图层信息")
    else:
        print("\n⚠️ alternative_visualizer存在问题")
        print("❌ 需要修复概览图的颜色获取逻辑")
