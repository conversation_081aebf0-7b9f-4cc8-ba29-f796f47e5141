#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实工作流程统计测试
更真实地模拟完整工作流程，包含详细的数据统计和颜色变化追踪
每一步操作都统计各组的颜色变化（包括调试输出和视图输出）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class WorkflowColorTracker:
    """工作流程颜色追踪器"""
    
    def __init__(self):
        self.step_counter = 0
        self.color_history = []
        self.statistics = {
            'total_entities': 0,
            'total_groups': 0,
            'color_changes': 0,
            'labeling_operations': 0,
            'color_scheme_applications': 0
        }
    
    def track_step(self, step_name, description, entities_data, groups_info, visualizer, processor):
        """追踪每一步的颜色变化"""
        self.step_counter += 1
        
        print(f"\n{'='*80}")
        print(f"📊 步骤 {self.step_counter}: {step_name}")
        print(f"📝 描述: {description}")
        print(f"{'='*80}")
        
        # 收集当前步骤的颜色数据
        step_data = {
            'step': self.step_counter,
            'name': step_name,
            'description': description,
            'timestamp': f"Step_{self.step_counter:02d}",
            'groups': []
        }
        
        # 统计每个组的颜色
        for group_idx, group in enumerate(entities_data):
            if group_idx < len(groups_info):
                group_info = groups_info[group_idx]
                group_colors = []
                
                print(f"\n🔍 组{group_idx+1} ({group_info.get('label', 'unknown')}) - 状态: {group_info.get('status', 'unknown')}")
                print(f"   实体数量: {len(group)}")
                print(f"   组显示颜色: {group_info.get('display_color', 'N/A')}")
                
                for entity_idx, entity in enumerate(group):
                    try:
                        # 🔧 获取调试输出的颜色（内部逻辑）
                        debug_color, debug_alpha, debug_linewidth, debug_status = visualizer._get_entity_display_info_enhanced(
                            entity,
                            labeled_entities=getattr(processor, 'labeled_entities', []),
                            current_group_entities=getattr(processor, 'current_group_entities', []),
                            all_groups=entities_data,
                            groups_info=groups_info,
                            processor=processor
                        )

                        # 🔧 获取视图输出的颜色（实际显示）
                        try:
                            # 模拟视图渲染时的颜色获取
                            view_color = visualizer.get_entity_color(entity)
                            view_alpha = 1.0
                            view_linewidth = 1.0
                            view_status = "view_rendered"
                        except:
                            # 如果没有get_entity_color方法，使用相同的逻辑
                            view_color = debug_color
                            view_alpha = debug_alpha
                            view_linewidth = debug_linewidth
                            view_status = f"view_{debug_status}"

                        entity_data = {
                            'entity_id': entity['id'],
                            'layer': entity['layer'],
                            'debug_color': debug_color,
                            'debug_alpha': debug_alpha,
                            'debug_linewidth': debug_linewidth,
                            'debug_status': debug_status,
                            'view_color': view_color,
                            'view_alpha': view_alpha,
                            'view_linewidth': view_linewidth,
                            'view_status': view_status
                        }
                        group_colors.append(entity_data)

                        # 详细输出 - 显示调试和视图两种颜色
                        print(f"     实体{entity_idx+1} (ID {entity['id']}, {entity['layer']}):")
                        print(f"       🔍 调试输出: {debug_color} - {debug_status}")
                        print(f"       🖼️ 视图输出: {view_color} - {view_status}")

                        # 检查调试输出和视图输出是否一致
                        if debug_color == view_color:
                            print(f"       ✅ 调试与视图颜色一致")
                        else:
                            print(f"       ⚠️ 调试与视图颜色不一致")

                    except Exception as e:
                        print(f"     实体{entity_idx+1} (ID {entity['id']}): ❌ 颜色获取失败: {e}")
                        entity_data = {
                            'entity_id': entity['id'],
                            'layer': entity['layer'],
                            'debug_color': 'ERROR',
                            'debug_alpha': 1.0,
                            'debug_linewidth': 1.0,
                            'debug_status': 'error',
                            'view_color': 'ERROR',
                            'view_alpha': 1.0,
                            'view_linewidth': 1.0,
                            'view_status': 'error'
                        }
                        group_colors.append(entity_data)
                
                # 组统计 - 分别统计调试和视图颜色
                debug_colors = list(set([e['debug_color'] for e in group_colors if e['debug_color'] != 'ERROR']))
                view_colors = list(set([e['view_color'] for e in group_colors if e['view_color'] != 'ERROR']))

                print(f"   🔍 调试输出颜色统计: {len(debug_colors)} 种颜色 - {debug_colors}")
                print(f"   🖼️ 视图输出颜色统计: {len(view_colors)} 种颜色 - {view_colors}")

                # 检查调试和视图颜色一致性
                colors_match = debug_colors == view_colors
                if colors_match:
                    print(f"   ✅ 调试与视图颜色完全一致")
                else:
                    print(f"   ⚠️ 调试与视图颜色存在差异")
                    debug_only = set(debug_colors) - set(view_colors)
                    view_only = set(view_colors) - set(debug_colors)
                    if debug_only:
                        print(f"     仅调试输出: {list(debug_only)}")
                    if view_only:
                        print(f"     仅视图输出: {list(view_only)}")

                group_data = {
                    'group_index': group_idx,
                    'group_label': group_info.get('label', 'unknown'),
                    'group_status': group_info.get('status', 'unknown'),
                    'group_display_color': group_info.get('display_color', 'N/A'),
                    'entity_count': len(group),
                    'debug_colors': debug_colors,
                    'view_colors': view_colors,
                    'colors_match': colors_match,
                    'entities': group_colors
                }
                step_data['groups'].append(group_data)
        
        # 添加到历史记录
        self.color_history.append(step_data)
        
        # 更新统计
        self.update_statistics(step_data)
        
        # 输出步骤总结
        self.print_step_summary(step_data)
        
        return step_data
    
    def update_statistics(self, step_data):
        """更新统计数据"""
        self.statistics['total_entities'] = sum([g['entity_count'] for g in step_data['groups']])
        self.statistics['total_groups'] = len(step_data['groups'])
        
        # 检测颜色变化
        if len(self.color_history) > 1:
            prev_step = self.color_history[-2]
            color_changes = 0
            
            for group_idx, group in enumerate(step_data['groups']):
                if group_idx < len(prev_step['groups']):
                    prev_group = prev_step['groups'][group_idx]
                    # 兼容新旧数据结构
                    current_colors = group.get('debug_colors', group.get('unique_colors', []))
                    prev_colors = prev_group.get('debug_colors', prev_group.get('unique_colors', []))
                    if current_colors != prev_colors:
                        color_changes += 1
            
            self.statistics['color_changes'] += color_changes
        
        # 检测标注操作
        if 'labeled' in step_data['name'].lower() or 'furniture' in step_data['name'].lower() or 'cabinet' in step_data['name'].lower():
            self.statistics['labeling_operations'] += 1
        
        # 检测配色应用
        if 'color' in step_data['name'].lower() or 'scheme' in step_data['name'].lower():
            self.statistics['color_scheme_applications'] += 1
    
    def print_step_summary(self, step_data):
        """输出步骤总结"""
        print(f"\n📈 步骤总结:")
        print(f"   总组数: {len(step_data['groups'])}")
        print(f"   总实体数: {sum([g['entity_count'] for g in step_data['groups']])}")
        
        # 按状态统计
        status_count = {}
        for group in step_data['groups']:
            status = group['group_status']
            status_count[status] = status_count.get(status, 0) + 1
        
        print(f"   组状态分布: {dict(status_count)}")
        
        # 按颜色统计 - 分别统计调试和视图颜色
        all_debug_colors = []
        all_view_colors = []
        colors_match_count = 0

        for group in step_data['groups']:
            all_debug_colors.extend(group['debug_colors'])
            all_view_colors.extend(group['view_colors'])
            if group['colors_match']:
                colors_match_count += 1

        unique_debug_colors = list(set(all_debug_colors))
        unique_view_colors = list(set(all_view_colors))

        print(f"   🔍 调试输出总颜色: {len(unique_debug_colors)} 种 - {unique_debug_colors}")
        print(f"   🖼️ 视图输出总颜色: {len(unique_view_colors)} 种 - {unique_view_colors}")
        print(f"   🎯 颜色一致性: {colors_match_count}/{len(step_data['groups'])} 组一致")

        # 整体一致性检查
        overall_match = unique_debug_colors == unique_view_colors
        if overall_match:
            print(f"   ✅ 调试与视图整体颜色完全一致")
        else:
            print(f"   ⚠️ 调试与视图整体颜色存在差异")
    
    def print_final_statistics(self):
        """输出最终统计"""
        print(f"\n{'='*80}")
        print(f"📊 最终统计报告")
        print(f"{'='*80}")

        print(f"🔢 基础统计:")
        print(f"   总步骤数: {self.step_counter}")
        print(f"   总实体数: {self.statistics['total_entities']}")
        print(f"   总组数: {self.statistics['total_groups']}")
        print(f"   颜色变化次数: {self.statistics['color_changes']}")
        print(f"   标注操作次数: {self.statistics['labeling_operations']}")
        print(f"   配色应用次数: {self.statistics['color_scheme_applications']}")

        # 颜色变化趋势分析 - 分别分析调试和视图颜色
        print(f"\n🎨 颜色变化趋势分析:")
        for i, step in enumerate(self.color_history):
            debug_colors = []
            view_colors = []
            for group in step['groups']:
                debug_colors.extend(group.get('debug_colors', group.get('unique_colors', [])))
                view_colors.extend(group.get('view_colors', group.get('unique_colors', [])))

            unique_debug_colors = list(set(debug_colors))
            unique_view_colors = list(set(view_colors))

            print(f"   步骤{i+1} ({step['name']}):")
            print(f"     🔍 调试输出: {len(unique_debug_colors)} 种颜色 - {unique_debug_colors}")
            print(f"     🖼️ 视图输出: {len(unique_view_colors)} 种颜色 - {unique_view_colors}")

            if unique_debug_colors == unique_view_colors:
                print(f"     ✅ 调试与视图颜色一致")
            else:
                print(f"     ⚠️ 调试与视图颜色不一致")

        # 组状态变化分析
        print(f"\n📋 组状态变化分析:")
        if len(self.color_history) > 0:
            first_step = self.color_history[0]
            last_step = self.color_history[-1]

            print(f"   初始状态分布:")
            first_status = {}
            for group in first_step['groups']:
                status = group['group_status']
                first_status[status] = first_status.get(status, 0) + 1
            for status, count in first_status.items():
                print(f"     {status}: {count} 组")

            print(f"   最终状态分布:")
            last_status = {}
            for group in last_step['groups']:
                status = group['group_status']
                last_status[status] = last_status.get(status, 0) + 1
            for status, count in last_status.items():
                print(f"     {status}: {count} 组")

        # 详细的颜色使用统计
        print(f"\n🌈 详细颜色使用统计:")
        color_usage = {}
        for step in self.color_history:
            for group in step['groups']:
                for color in group['unique_colors']:
                    if color not in color_usage:
                        color_usage[color] = {'steps': [], 'groups': []}
                    color_usage[color]['steps'].append(step['name'])
                    color_usage[color]['groups'].append(f"组{group['group_index']+1}")

        for color, usage in color_usage.items():
            unique_steps = list(set(usage['steps']))
            unique_groups = list(set(usage['groups']))
            print(f"   {color}: 出现在 {len(unique_steps)} 个步骤, {len(unique_groups)} 个组")
            print(f"     步骤: {unique_steps}")
            print(f"     组: {unique_groups}")

        # 组标签变化追踪
        print(f"\n🏷️ 组标签变化追踪:")
        if len(self.color_history) > 1:
            for group_idx in range(self.statistics['total_groups']):
                labels = []
                for step in self.color_history:
                    if group_idx < len(step['groups']):
                        labels.append(step['groups'][group_idx]['group_label'])

                unique_labels = list(dict.fromkeys(labels))  # 保持顺序的去重
                if len(unique_labels) > 1:
                    print(f"   组{group_idx+1}: {' → '.join(unique_labels)}")
                else:
                    print(f"   组{group_idx+1}: {unique_labels[0]} (无变化)")

        # 调试与视图颜色一致性分析
        print(f"\n🔍🖼️ 调试与视图颜色一致性分析:")
        total_consistency_checks = 0
        consistent_steps = 0

        for i, step in enumerate(self.color_history):
            step_consistent = True
            for group in step['groups']:
                if not group.get('colors_match', True):
                    step_consistent = False
                    break

            total_consistency_checks += 1
            if step_consistent:
                consistent_steps += 1
                print(f"   步骤{i+1} ({step['name']}): ✅ 完全一致")
            else:
                print(f"   步骤{i+1} ({step['name']}): ⚠️ 存在差异")

                # 详细显示差异
                for group in step['groups']:
                    if not group.get('colors_match', True):
                        debug_colors = group.get('debug_colors', [])
                        view_colors = group.get('view_colors', [])
                        print(f"     组{group['group_index']+1}: 调试={debug_colors}, 视图={view_colors}")

        consistency_rate = (consistent_steps / total_consistency_checks * 100) if total_consistency_checks > 0 else 0
        print(f"   整体一致性: {consistent_steps}/{total_consistency_checks} ({consistency_rate:.1f}%)")

        if consistency_rate == 100:
            print(f"   🎉 调试输出与视图输出完全一致！")
        elif consistency_rate >= 80:
            print(f"   ✅ 调试输出与视图输出基本一致")
        else:
            print(f"   ⚠️ 调试输出与视图输出存在较大差异，需要检查")
    
    def compare_steps(self, step1_idx, step2_idx):
        """比较两个步骤的颜色变化"""
        if step1_idx >= len(self.color_history) or step2_idx >= len(self.color_history):
            print("❌ 步骤索引超出范围")
            return
        
        step1 = self.color_history[step1_idx]
        step2 = self.color_history[step2_idx]
        
        print(f"\n🔄 步骤比较: {step1['name']} vs {step2['name']}")
        print("-" * 60)
        
        for group_idx in range(min(len(step1['groups']), len(step2['groups']))):
            group1 = step1['groups'][group_idx]
            group2 = step2['groups'][group_idx]
            
            colors1 = set(group1['unique_colors'])
            colors2 = set(group2['unique_colors'])
            
            if colors1 != colors2:
                print(f"   组{group_idx+1} ({group1['group_label']}):")
                print(f"     步骤{step1_idx+1}: {list(colors1)}")
                print(f"     步骤{step2_idx+1}: {list(colors2)}")
                print(f"     变化: {'✅ 颜色改变' if colors1 != colors2 else '⚪ 颜色不变'}")

def create_realistic_test_data():
    """创建更真实的测试数据"""
    print("🏗️ 创建真实测试数据")
    print("=" * 60)
    
    test_entities = []
    entity_id = 1
    
    # 2个墙体组，每组2个线条（模拟房间墙体）
    wall_groups = []
    wall_positions = [
        [(0, 0, 100, 0), (100, 0, 100, 100)],      # 房间1的两面墙
        [(200, 0, 300, 0), (300, 0, 300, 100)]     # 房间2的两面墙
    ]
    
    for group_idx, positions in enumerate(wall_positions):
        group_entities = []
        for pos_idx, (x1, y1, x2, y2) in enumerate(positions):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': [x1, y1],
                'end': [x2, y2],
                'length': ((x2-x1)**2 + (y2-y1)**2)**0.5
            }
            group_entities.append(entity)
            test_entities.append(entity)
            entity_id += 1
        wall_groups.append(group_entities)
        lengths = [f"{e['length']:.1f}" for e in group_entities]
        print(f"   墙体组{group_idx+1}: ID {[e['id'] for e in group_entities]} - 长度 {lengths}")
    
    # 2个门窗组，每组2个线条（模拟门窗开口）
    door_window_groups = []
    door_window_data = [
        [('A-WINDOW', 20, 0, 40, 0), ('A-DOOR', 60, 0, 80, 0)],    # 房间1的门窗
        [('A-WINDOW', 220, 0, 240, 0), ('A-DOOR', 260, 0, 280, 0)] # 房间2的门窗
    ]
    
    for group_idx, door_window_list in enumerate(door_window_data):
        group_entities = []
        for layer, x1, y1, x2, y2 in door_window_list:
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': layer,
                'start': [x1, y1],
                'end': [x2, y2],
                'length': ((x2-x1)**2 + (y2-y1)**2)**0.5
            }
            group_entities.append(entity)
            test_entities.append(entity)
            entity_id += 1
        door_window_groups.append(group_entities)
        layers = [e['layer'] for e in group_entities]
        print(f"   门窗组{group_idx+1}: ID {[e['id'] for e in group_entities]} - 图层 {layers}")
    
    # 4个其他组，每组2个线条（模拟家具和其他元素）
    other_groups = []
    other_data = [
        ('FURNITURE', [(30, 30, 70, 30), (30, 70, 70, 70)]),      # 桌子
        ('0', [(10, 10, 90, 10), (10, 90, 90, 90)]),              # 辅助线
        ('TEXT', [(50, 50, 50, 60), (55, 50, 55, 60)]),           # 文字标注
        ('DIMENSION', [(0, 110, 100, 110), (200, 110, 300, 110)]) # 尺寸标注
    ]
    
    for group_idx, (layer, positions) in enumerate(other_data):
        group_entities = []
        for x1, y1, x2, y2 in positions:
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': layer,
                'start': [x1, y1],
                'end': [x2, y2],
                'length': ((x2-x1)**2 + (y2-y1)**2)**0.5
            }
            group_entities.append(entity)
            test_entities.append(entity)
            entity_id += 1
        other_groups.append(group_entities)
        print(f"   其他组{group_idx+1}: ID {[e['id'] for e in group_entities]} - 图层 {layer}")
    
    all_groups = wall_groups + door_window_groups + other_groups
    
    print(f"\n📊 数据总结:")
    print(f"   总实体数: {len(test_entities)}")
    print(f"   总组数: {len(all_groups)}")
    print(f"   图层分布: {list(set([e['layer'] for e in test_entities]))}")
    
    return test_entities, all_groups, wall_groups, door_window_groups, other_groups

def test_realistic_workflow_with_statistics():
    """测试真实工作流程并进行详细统计"""
    print("🧪 真实工作流程统计测试")
    print("=" * 80)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用创建完成")
        
        # 创建颜色追踪器
        tracker = WorkflowColorTracker()
        
        # 创建测试数据
        test_entities, all_groups, wall_groups, door_window_groups, other_groups = create_realistic_test_data()
        
        # 初始化处理器
        app.processor.current_file_entities = test_entities
        visualizer = app.visualizer
        
        # 步骤1：初始状态 - 所有实体未分组
        app.processor.all_groups = []
        app.processor.groups_info = []
        app.processor.auto_labeled_entities = []
        app.processor.labeled_entities = []
        
        # 为了统计，创建临时的单实体组
        temp_groups = [[entity] for entity in test_entities]
        temp_groups_info = []
        for i, entity in enumerate(test_entities):
            temp_groups_info.append({
                'index': i,
                'label': 'unlabeled',
                'status': 'unlabeled',
                'display_color': '#D3D3D3'
            })
        
        tracker.track_step(
            "初始状态",
            "程序启动，所有实体未分组未标注",
            temp_groups,
            temp_groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤2：自动识别分组
        app.processor.all_groups = all_groups
        groups_info = []
        group_labels = ['wall', 'wall', 'door_window', 'door_window', 'other', 'other', 'other', 'other']
        
        for i, (group, label) in enumerate(zip(all_groups, group_labels)):
            group_info = {
                'index': i,
                'label': label,
                'entity_count': len(group),
                'status': 'auto_labeled',
                'confidence': 0.8 + i * 0.02,  # 模拟不同的置信度
                'group_type': label,
                'layer': group[0]['layer'],
                'display_color': app.current_color_scheme.get(label, '#808080')
            }
            groups_info.append(group_info)
        
        app.processor.groups_info = groups_info
        app.processor.auto_labeled_entities = [entity for group in all_groups for entity in group]
        
        tracker.track_step(
            "自动识别分组",
            "AI自动识别并分组，应用初始颜色",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤3：手动标注家具组
        furniture_group_indices = [4, 5]  # 其他组1和2
        for idx in furniture_group_indices:
            groups_info[idx]['label'] = 'furniture'
            groups_info[idx]['status'] = 'labeled'
            groups_info[idx]['group_type'] = 'furniture'
            groups_info[idx]['display_color'] = app.current_color_scheme.get('furniture', '#0000FF')
        
        furniture_entities = [entity for idx in furniture_group_indices for entity in all_groups[idx]]
        app.processor.labeled_entities.extend(furniture_entities)
        
        tracker.track_step(
            "标注家具类型",
            "手动将组5和组6标注为家具类型",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤4：应用默认配色方案
        print(f"\n🎨 应用默认配色方案...")
        app.current_color_scheme = app.default_color_scheme.copy()
        app.visualizer.set_color_scheme(app.current_color_scheme)
        
        # 更新组显示颜色
        for group_info in groups_info:
            group_info['display_color'] = app.current_color_scheme.get(group_info['label'], '#808080')
        
        tracker.track_step(
            "应用默认配色",
            "应用默认配色方案，更新所有组的显示颜色",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤5：标注柜子类型
        cabinet_group_idx = 6  # 其他组3
        groups_info[cabinet_group_idx]['label'] = 'cabinet'
        groups_info[cabinet_group_idx]['status'] = 'labeled'
        groups_info[cabinet_group_idx]['group_type'] = 'cabinet'
        groups_info[cabinet_group_idx]['display_color'] = app.current_color_scheme.get('cabinet', '#00FFFF')
        
        cabinet_entities = all_groups[cabinet_group_idx]
        app.processor.labeled_entities.extend(cabinet_entities)
        
        tracker.track_step(
            "标注柜子类型",
            "手动将组7标注为柜子类型",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤6：再次应用默认配色
        print(f"\n🎨 再次应用默认配色方案...")
        app.current_color_scheme = app.default_color_scheme.copy()
        app.visualizer.set_color_scheme(app.current_color_scheme)
        
        for group_info in groups_info:
            group_info['display_color'] = app.current_color_scheme.get(group_info['label'], '#808080')
        
        tracker.track_step(
            "再次应用默认配色",
            "第二次应用默认配色方案，确保颜色一致性",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 步骤7：标注餐桌类型
        table_group_idx = 7  # 其他组4
        groups_info[table_group_idx]['label'] = 'table'
        groups_info[table_group_idx]['status'] = 'labeled'
        groups_info[table_group_idx]['group_type'] = 'table'
        groups_info[table_group_idx]['display_color'] = app.current_color_scheme.get('table', '#4169E1')
        
        table_entities = all_groups[table_group_idx]
        app.processor.labeled_entities.extend(table_entities)
        
        tracker.track_step(
            "标注餐桌类型",
            "手动将组8标注为餐桌类型，完成所有标注",
            all_groups,
            groups_info,
            visualizer,
            app.processor
        )
        
        # 输出最终统计
        tracker.print_final_statistics()
        
        # 步骤比较分析
        print(f"\n🔄 关键步骤比较分析:")
        print("-" * 60)
        
        # 比较初始状态和最终状态
        tracker.compare_steps(0, len(tracker.color_history) - 1)
        
        # 比较配色应用前后
        if len(tracker.color_history) >= 4:
            print(f"\n🎨 配色应用效果分析:")
            tracker.compare_steps(2, 3)  # 标注家具 vs 应用配色
        
        # 清理
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        try:
            root.destroy()
        except:
            pass
        return False

if __name__ == "__main__":
    print("🧪 开始真实工作流程统计测试")
    
    if test_realistic_workflow_with_statistics():
        print(f"\n🎉 真实工作流程统计测试完成")
        print(f"💡 测试包含了完整的颜色变化追踪和统计分析")
        print(f"🚀 所有步骤的数据统计和颜色变化都已详细记录！")
    else:
        print(f"\n🔧 测试过程中遇到问题，但已收集了大量统计数据")
