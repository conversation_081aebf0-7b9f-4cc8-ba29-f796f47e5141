#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的索引图显示效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 导入优化后的可视化器
from cad_visualizer import CADVisualizer

def test_optimized_legend():
    """测试优化后的索引图"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("索引图优化测试")
    root.geometry("800x600")
    
    try:
        # 创建优化后的可视化器
        visualizer = CADVisualizer()
        
        # 设置测试配色方案
        test_color_scheme = {
            'wall': '#FF6B6B',
            'door_window': '#4ECDC4', 
            'furniture': '#45B7D1',
            'column': '#96CEB4',
            'stair': '#FFEAA7',
            'other': '#DDA0DD',
            'current_group': '#FF0000',
            'text': '#000000'
        }
        
        visualizer.update_color_scheme(test_color_scheme)
        
        # 创建测试数据
        color_stats = {
            '#FF0000': 1,  # 当前组
            '#FF6B6B': 3,  # 墙体
            '#4ECDC4': 2,  # 门窗
            '#45B7D1': 4,  # 家具
            '#DDA0DD': 1   # 其他
        }
        
        group_status_stats = {
            'current': 1,
            'labeled': 4,
            'auto_labeled': 2,
            'unlabeled': 3
        }
        
        # 测试索引图创建
        print("🧪 开始测试优化后的索引图...")
        visualizer._create_color_index_chart(color_stats, group_status_stats)
        
        # 创建画布并显示
        canvas = FigureCanvasTkAgg(visualizer.get_figure(), root)
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # 添加说明文字
        info_frame = tk.Frame(root)
        info_frame.pack(fill='x', pady=5)
        
        info_text = """
优化效果测试：
1. ✅ 去除了顶部的"索引图"标题栏
2. ✅ 改进了排列方式，分离组状态和实体类别
3. ✅ 调整了颜色方块大小，适应紧凑布局
4. ✅ 简化了标签文字，去除冗余信息
5. ✅ 动态调整列数，避免过度拥挤
        """
        
        info_label = tk.Label(info_frame, text=info_text, 
                             font=('Arial', 9), justify='left')
        info_label.pack(anchor='w', padx=10)
        
        # 添加关闭按钮
        close_btn = tk.Button(root, text="关闭测试", 
                             command=root.destroy,
                             font=('Arial', 10))
        close_btn.pack(pady=5)
        
        print("✅ 索引图优化测试窗口已创建")
        print("📋 优化内容：")
        print("   - 去除顶部标题栏，节省空间")
        print("   - 分离组状态和实体类别显示")
        print("   - 调整颜色方块大小和间距")
        print("   - 简化标签文字")
        print("   - 动态调整列数")
        
        # 启动测试窗口
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 索引图优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("测试失败", f"索引图优化测试失败: {e}")

def test_layout_comparison():
    """对比优化前后的布局效果"""
    
    print("🔄 开始布局对比测试...")
    
    # 测试不同数量的项目
    test_cases = [
        {"name": "少量项目(4个)", "items": 4},
        {"name": "中等项目(8个)", "items": 8}, 
        {"name": "大量项目(12个)", "items": 12}
    ]
    
    for case in test_cases:
        print(f"\n📊 测试 {case['name']}:")
        
        total_items = case['items']
        
        # 优化后的列数计算
        if total_items <= 4:
            cols = 2
        elif total_items <= 8:
            cols = 3
        else:
            cols = 4
            
        rows = (total_items + cols - 1) // cols
        
        print(f"   项目数: {total_items}")
        print(f"   列数: {cols}")
        print(f"   行数: {rows}")
        print(f"   布局: {cols}列 x {rows}行")
        
        # 计算空间利用率
        used_cells = total_items
        total_cells = cols * rows
        utilization = (used_cells / total_cells) * 100
        
        print(f"   空间利用率: {utilization:.1f}%")
        
        if utilization >= 75:
            print("   ✅ 空间利用率良好")
        elif utilization >= 50:
            print("   ⚠️ 空间利用率一般")
        else:
            print("   ❌ 空间利用率较低")

if __name__ == "__main__":
    print("🎨 索引图优化测试程序")
    print("=" * 50)
    
    # 运行布局对比测试
    test_layout_comparison()
    
    print("\n" + "=" * 50)
    print("🖼️ 启动可视化测试...")
    
    # 运行可视化测试
    test_optimized_legend()
