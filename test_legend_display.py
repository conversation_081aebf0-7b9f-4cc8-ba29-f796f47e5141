#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试索引图显示
检查索引图是否正常显示
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_legend_display():
    """测试索引图显示"""
    print("🔍 开始测试索引图显示...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("索引图显示测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(2)
        
        # 测试1：检查索引图组件是否存在
        print("\n🔍 测试1：检查索引图组件是否存在")
        
        legend_components = [
            'legend_frame',
            'legend_fig',
            'legend_ax',
            'legend_canvas',
            'legend_font'
        ]
        
        for component in legend_components:
            if hasattr(app, component):
                value = getattr(app, component)
                print(f"    ✅ {component}: {type(value)}")
            else:
                print(f"    ❌ {component}: 不存在")
        
        # 测试2：检查索引图方法是否存在
        print("\n🔍 测试2：检查索引图方法是否存在")
        
        legend_methods = [
            '_create_legend_panel',
            '_update_legend_display',
            '_draw_legend_content'
        ]
        
        for method in legend_methods:
            if hasattr(app, method):
                print(f"    ✅ {method}: 存在")
            else:
                print(f"    ❌ {method}: 不存在")
        
        # 测试3：检查处理器和组数据
        print("\n🔍 测试3：检查处理器和组数据")
        
        if hasattr(app, 'processor') and app.processor:
            processor = app.processor
            print(f"    处理器存在: {type(processor)}")
            
            # 检查组数据
            if hasattr(processor, 'all_groups'):
                all_groups = processor.all_groups
                print(f"    all_groups: {len(all_groups) if all_groups else 0} 个组")
                
                if all_groups:
                    total_entities = sum(len(group) if isinstance(group, list) else 0 for group in all_groups)
                    print(f"    总实体数: {total_entities}")
            
            # 检查组信息
            if hasattr(processor, 'get_groups_info'):
                try:
                    groups_info = processor.get_groups_info()
                    print(f"    groups_info: {len(groups_info) if groups_info else 0} 个组信息")
                    
                    if groups_info:
                        for i, info in enumerate(groups_info[:3]):  # 显示前3个
                            status = info.get('status', 'unknown')
                            entity_count = info.get('entity_count', 0)
                            print(f"      组{i+1}: status={status}, entities={entity_count}")
                except Exception as e:
                    print(f"    ❌ 获取组信息失败: {e}")
        else:
            print(f"    ❌ 处理器不存在")
        
        # 测试4：手动调用索引图更新
        print("\n🔍 测试4：手动调用索引图更新")
        
        try:
            if hasattr(app, '_update_legend_display'):
                print("    调用 _update_legend_display...")
                app._update_legend_display()
                print("    ✅ 索引图更新调用成功")
            else:
                print("    ❌ _update_legend_display 方法不存在")
        except Exception as e:
            print(f"    ❌ 索引图更新失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试5：检查索引图画布内容
        print("\n🔍 测试5：检查索引图画布内容")
        
        try:
            if hasattr(app, 'legend_ax') and app.legend_ax:
                ax = app.legend_ax
                print(f"    索引图轴对象: {ax}")
                
                # 检查轴的内容
                children = ax.get_children()
                print(f"    轴子对象数量: {len(children)}")
                
                # 检查文本对象
                texts = [child for child in children if hasattr(child, 'get_text')]
                print(f"    文本对象数量: {len(texts)}")
                
                for i, text in enumerate(texts[:5]):  # 显示前5个文本
                    try:
                        text_content = text.get_text()
                        position = text.get_position()
                        print(f"      文本{i+1}: '{text_content}' at {position}")
                    except:
                        print(f"      文本{i+1}: 无法获取内容")
                
                # 检查矩形对象（颜色方块）
                rectangles = [child for child in children if hasattr(child, 'get_facecolor')]
                print(f"    矩形对象数量: {len(rectangles)}")
                
                for i, rect in enumerate(rectangles[:5]):  # 显示前5个矩形
                    try:
                        color = rect.get_facecolor()
                        print(f"      矩形{i+1}: 颜色 {color}")
                    except:
                        print(f"      矩形{i+1}: 无法获取颜色")
            else:
                print(f"    ❌ 索引图轴对象不存在")
        except Exception as e:
            print(f"    ❌ 检查索引图画布内容失败: {e}")
        
        # 测试6：检查索引图是否可见
        print("\n🔍 测试6：检查索引图是否可见")
        
        try:
            if hasattr(app, 'legend_frame') and app.legend_frame:
                frame = app.legend_frame
                print(f"    索引图框架: {frame}")
                print(f"    框架可见性: {frame.winfo_viewable()}")
                print(f"    框架大小: {frame.winfo_width()}x{frame.winfo_height()}")
                print(f"    框架位置: ({frame.winfo_x()}, {frame.winfo_y()})")
                
                if hasattr(app, 'legend_canvas') and app.legend_canvas:
                    canvas = app.legend_canvas
                    widget = canvas.get_tk_widget()
                    print(f"    画布组件: {widget}")
                    print(f"    画布可见性: {widget.winfo_viewable()}")
                    print(f"    画布大小: {widget.winfo_width()}x{widget.winfo_height()}")
            else:
                print(f"    ❌ 索引图框架不存在")
        except Exception as e:
            print(f"    ❌ 检查索引图可见性失败: {e}")
        
        # 等待一段时间让用户观察
        print("\n⏳ 等待3秒让用户观察界面...")
        time.sleep(3)
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 索引图显示测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_legend_display()
    if success:
        print("\n🎊 索引图显示测试成功！")
    else:
        print("\n⚠️ 索引图显示测试发现问题")
