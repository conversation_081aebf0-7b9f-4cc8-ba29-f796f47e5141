#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化的调试功能测试
验证新的调试输出格式
"""

def test_import():
    """测试导入"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_methods_exist():
    """测试方法存在性"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 检查方法是否存在
        methods = [
            'select_category',
            '_debug_group_labeling_result',
            '_debug_grouping_completion',
            '_get_group_color_info'
        ]
        
        for method in methods:
            if hasattr(EnhancedCADAppV2, method):
                print(f"✅ {method}: 存在")
            else:
                print(f"❌ {method}: 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_debug_output_format():
    """测试调试输出格式"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import inspect
        
        # 检查_debug_grouping_completion方法的源代码
        method = getattr(EnhancedCADAppV2, '_debug_grouping_completion', None)
        if method:
            source = inspect.getsource(method)
            
            # 检查新的调试输出格式
            expected_patterns = [
                "总组数:",
                "组{group_id}: ID=",
                "类型=",
                "实体数=",
                "颜色=",
                "主要实体类型=",
                "主要图层=",
                "状态="
            ]
            
            print("🔍 检查新的调试输出格式:")
            for pattern in expected_patterns:
                if pattern in source:
                    print(f"  ✅ {pattern}: 存在")
                else:
                    print(f"  ❌ {pattern}: 缺失")
            
            return True
        else:
            print("❌ _debug_grouping_completion方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_color_info_method():
    """测试颜色信息方法"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import inspect
        
        # 检查_get_group_color_info方法的源代码
        method = getattr(EnhancedCADAppV2, '_get_group_color_info', None)
        if method:
            source = inspect.getsource(method)
            
            # 检查关键功能
            key_features = [
                "_get_entity_display_info_enhanced",
                "current_color_scheme",
                "状态:",
                "透明度:",
                "配色方案:",
                "获取颜色失败:"
            ]
            
            print("🎨 检查颜色信息方法:")
            for feature in key_features:
                if feature in source:
                    print(f"  ✅ {feature}: 存在")
                else:
                    print(f"  ❌ {feature}: 缺失")
            
            return True
        else:
            print("❌ _get_group_color_info方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动简化调试功能测试")
    print("="*50)
    
    # 测试1：导入
    test1_result = test_import()
    
    # 测试2：方法存在性
    test2_result = test_methods_exist()
    
    # 测试3：调试输出格式
    test3_result = test_debug_output_format()
    
    # 测试4：颜色信息方法
    test4_result = test_color_info_method()
    
    print(f"\n" + "="*50)
    print("📊 简化调试功能测试结果")
    print("="*50)
    
    print(f"🧪 导入测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"🧪 方法存在性: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"🧪 调试输出格式: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"🧪 颜色信息方法: {'✅ 通过' if test4_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result and test3_result and test4_result
    
    if overall_success:
        print(f"\n🎉 所有测试通过！新的调试功能已正确集成")
        print(f"\n💡 新的调试输出格式:")
        print(f"1. 📊 总组数显示（当总组数≥1时）")
        print(f"2. 🏷️ 每个组的ID、类型、实体数")
        print(f"3. 🎨 每个组的颜色输出")
        print(f"4. 📋 主要实体类型和图层信息")
        print(f"5. 📈 组状态详细信息")
        print(f"6. 🌈 实体类型和标签分布")
        
        print(f"\n🔧 期望的调试输出示例:")
        print(f"📋 各组详细信息 (总组数: 2):")
        print(f"  组1: ID=1, 类型=wall, 实体数=5, 颜色=#8B4513 (状态:auto_labeled)")
        print(f"       主要实体类型=LINE, 主要图层=A-WALL, 状态=自动标注(5)")
        print(f"       标签分布: wall(5)")
        print(f"")
        print(f"  组2: ID=2, 类型=unlabeled, 实体数=3, 颜色=#808080 (未标注)")
        print(f"       主要实体类型=LINE, 主要图层=A-DOOR, 状态=待标注")
        
    else:
        print(f"\n⚠️ 部分测试失败，需要检查相关功能")

if __name__ == "__main__":
    main()
