#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试批量概览修复效果（更新版）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_batch_overview_conditions():
    """测试批量概览触发条件"""

    print("🔧 测试批量概览触发条件")
    print("=" * 40)

    try:
        from visualization_performance_fix import VisualizationPerformanceFix

        fix = VisualizationPerformanceFix()

        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'door_window': '#FFD700',
                    'current_group': '#FF0000',
                    'unlabeled': '#C0C0C0'
                }
                self.ax_overview = MockAxis()

        class MockAxis:
            def clear(self): pass
            def set_aspect(self, aspect): pass
            def grid(self, *args, **kwargs): pass
            def plot(self, *args, **kwargs): pass
            def set_title(self, *args, **kwargs): pass

        # 模拟处理器
        class MockProcessor:
            def __init__(self, has_groups=True):
                if has_groups:
                    self.all_groups = [['entity1'], ['entity2']]
                    self.groups_info = [
                        {'status': 'labeled', 'label': 'wall'},
                        {'status': 'labeled', 'label': 'door_window'}
                    ]

        visualizer = MockVisualizer()

        # 测试用例
        test_cases = [
            {
                'name': '有处理器和组信息，少量实体',
                'entity_count': 50,
                'processor': MockProcessor(has_groups=True),
                'expected_batch': True
            },
            {
                'name': '有处理器和组信息，大量实体',
                'entity_count': 455,
                'processor': MockProcessor(has_groups=True),
                'expected_batch': True
            },
            {
                'name': '无处理器，少量实体',
                'entity_count': 50,
                'processor': None,
                'expected_batch': False
            },
            {
                'name': '无处理器，大量实体',
                'entity_count': 455,
                'processor': None,
                'expected_batch': True  # 大量实体时仍使用批量模式
            }
        ]

        for i, case in enumerate(test_cases):
            print(f"\n测试 {i+1}: {case['name']}")

            # 创建测试实体
            test_entities = []
            for j in range(case['entity_count']):
                test_entities.append({
                    'type': 'LINE',
                    'layer': f'Layer_{j}',
                    'points': [[j, 0], [j+1, 0]]
                })

            # 测试批量概览触发
            kwargs = {}
            if case['processor']:
                kwargs['processor'] = case['processor']

            result = fix.optimize_visualize_overview(
                visualizer, test_entities, [], [], **kwargs
            )

            print(f"  实体数量: {case['entity_count']}")
            print(f"  有处理器: {case['processor'] is not None}")
            print(f"  期望批量: {case['expected_batch']}")
            print(f"  实际结果: {result}")

            if result == case['expected_batch']:
                print(f"  ✅ 测试通过")
            else:
                print(f"  ❌ 测试失败")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_fallback():
    """测试颜色获取回退机制"""

    print("\n🎨 测试颜色获取回退机制")
    print("=" * 40)

    try:
        from visualization_performance_fix import VisualizationPerformanceFix

        fix = VisualizationPerformanceFix()

        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'unlabeled': '#C0C0C0',
                    'other': '#808080'
                }

            def _get_entity_display_info_enhanced(self, entity, **kwargs):
                # 模拟增强颜色获取方法
                if entity.get('label') == 'wall':
                    return '#8B4513', 1.0, 1.0, 'labeled'
                else:
                    return '#C0C0C0', 1.0, 1.0, 'unlabeled'

        visualizer = MockVisualizer()

        # 测试实体
        test_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'label': 'wall',
            'points': [[0, 0], [10, 0]]
        }

        # 测试场景
        scenarios = [
            {
                'name': '有组信息',
                'all_groups': [[test_entity]],
                'groups_info': [{'status': 'labeled', 'label': 'wall', 'is_current_group': False}],
                'expected_color': '#8B4513'
            },
            {
                'name': '无组信息，有增强方法',
                'all_groups': [],
                'groups_info': [],
                'expected_color': '#8B4513'  # 通过增强方法获取
            },
            {
                'name': '无组信息，无增强方法',
                'all_groups': [],
                'groups_info': [],
                'expected_color': '#C0C0C0',  # 默认颜色
                'remove_enhanced': True
            }
        ]

        for i, scenario in enumerate(scenarios):
            print(f"\n场景 {i+1}: {scenario['name']}")

            # 临时移除增强方法（如果需要）
            original_method = None
            if scenario.get('remove_enhanced'):
                if hasattr(visualizer, '_get_entity_display_info_enhanced'):
                    original_method = visualizer._get_entity_display_info_enhanced
                    delattr(visualizer, '_get_entity_display_info_enhanced')

            try:
                color = fix._get_entity_color_for_batch_enhanced(
                    test_entity,
                    visualizer.color_scheme,
                    visualizer,
                    [],
                    scenario['all_groups'],
                    scenario['groups_info'],
                    None
                )

                print(f"  获取颜色: {color}")
                print(f"  期望颜色: {scenario['expected_color']}")

                if color == scenario['expected_color']:
                    print(f"  ✅ 颜色正确")
                else:
                    print(f"  ❌ 颜色错误")

            finally:
                # 恢复增强方法
                if original_method:
                    visualizer._get_entity_display_info_enhanced = original_method

        return True

    except Exception as e:
        print(f"❌ 颜色回退测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 批量概览修复测试程序")
    print("=" * 50)

    # 测试批量概览触发条件
    test1_result = test_batch_overview_conditions()

    # 测试颜色获取回退机制
    test2_result = test_color_fallback()

    print("\n" + "=" * 50)
    print("🎯 测试总结:")

    if test1_result and test2_result:
        print("✅ 所有测试通过")
        print("✅ 批量概览触发条件正确")
        print("✅ 颜色获取回退机制正常")
        print("\n🎉 修复效果:")
        print("  1. 有组信息时使用批量概览，显示正确颜色")
        print("  2. 无组信息时回退到原始方法，避免灰色显示")
        print("  3. 多层回退机制确保总能获取到合适的颜色")
    else:
        print("❌ 部分测试失败")
        if not test1_result:
            print("❌ 批量概览触发条件需要调整")
        if not test2_result:
            print("❌ 颜色获取回退机制需要完善")
