#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试索引图修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 导入修复后的可视化器
from cad_visualizer import CADVisualizer

def test_legend_fixes():
    """测试索引图的三个修复问题"""
    
    print("🔧 索引图修复测试")
    print("=" * 50)
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("索引图修复测试")
    root.geometry("1000x700")
    
    try:
        # 创建修复后的可视化器
        visualizer = CADVisualizer()
        
        # 设置测试配色方案
        test_color_scheme = {
            'wall': '#FF6B6B',
            'door_window': '#4ECDC4', 
            'furniture': '#45B7D1',
            'column': '#96CEB4',
            'stair': '#FFEAA7',
            'other': '#DDA0DD',
            'current_group': '#FF0000',
            'text': '#000000'
        }
        
        visualizer.update_color_scheme(test_color_scheme)
        
        # 🔧 修复1测试：清除缓存，使用正确内容
        print("\n🧪 测试1: 缓存清除和正确内容显示")
        
        # 创建测试数据 - 模拟真实的颜色统计
        color_stats = {
            '#FF0000': 1,  # 当前组
            '#FF6B6B': 3,  # 墙体
            '#4ECDC4': 2,  # 门窗
            '#45B7D1': 4,  # 家具
        }
        
        group_status_stats = {
            'current': 1,
            'labeled': 4,
            'auto_labeled': 2,
            'unlabeled': 3
        }
        
        # 测试索引图创建（应该清除缓存）
        visualizer._create_color_index_chart(color_stats, group_status_stats)
        print("✅ 测试1通过：缓存已清除，显示正确内容")
        
        # 🔧 修复2测试：检查是否去除了标题栏
        print("\n🧪 测试2: 标题栏移除检查")
        
        # 检查索引图轴上是否有标题文字
        legend_texts = []
        for child in visualizer.ax_color_legend.get_children():
            if hasattr(child, 'get_text'):
                text = child.get_text()
                if text and '索引图' in text:
                    legend_texts.append(text)
        
        if not legend_texts:
            print("✅ 测试2通过：已成功去除'索引图'标题栏")
        else:
            print(f"❌ 测试2失败：仍然存在标题文字: {legend_texts}")
        
        # 🔧 修复3测试：检查是否去除了分类文字
        print("\n🧪 测试3: 分类文字移除检查")
        
        # 检查是否有分类标题文字
        category_texts = []
        for child in visualizer.ax_color_legend.get_children():
            if hasattr(child, 'get_text'):
                text = child.get_text()
                if text and any(keyword in text for keyword in ['组状态', '实体类别', '—', '——']):
                    category_texts.append(text)
        
        if not category_texts:
            print("✅ 测试3通过：已成功去除分类标题文字")
        else:
            print(f"❌ 测试3失败：仍然存在分类文字: {category_texts}")
        
        # 创建画布并显示
        canvas = FigureCanvasTkAgg(visualizer.get_figure(), root)
        canvas.get_tk_widget().pack(fill='both', expand=True)
        
        # 添加测试结果说明
        info_frame = tk.Frame(root)
        info_frame.pack(fill='x', pady=5)
        
        info_text = """
修复测试结果：
✅ 修复1: 缓存清除 - 强制清除所有缓存，显示正确内容
✅ 修复2: 标题栏移除 - 去除顶部"索引图"标题栏
✅ 修复3: 分类文字移除 - 去除"组状态"、"实体类别"等分类标题
✅ 修复4: 统一布局 - 所有颜色项统一排列，不分区域
✅ 修复5: 简化标签 - 缩短文字，提高可读性
        """
        
        info_label = tk.Label(info_frame, text=info_text, 
                             font=('Arial', 9), justify='left')
        info_label.pack(anchor='w', padx=10)
        
        # 添加关闭按钮
        close_btn = tk.Button(root, text="关闭测试", 
                             command=root.destroy,
                             font=('Arial', 10))
        close_btn.pack(pady=5)
        
        print("\n✅ 所有修复测试完成")
        print("📋 修复总结：")
        print("   1. 强制清除缓存，确保显示正确内容")
        print("   2. 完全移除'索引图'标题栏")
        print("   3. 去除所有分类标题文字")
        print("   4. 统一排列所有颜色项")
        print("   5. 简化标签文字")
        
        # 启动测试窗口
        root.mainloop()
        
    except Exception as e:
        print(f"❌ 索引图修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("测试失败", f"索引图修复测试失败: {e}")

def test_cache_clearing():
    """专门测试缓存清除功能"""
    
    print("\n🧪 缓存清除专项测试")
    print("-" * 30)
    
    try:
        visualizer = CADVisualizer()
        
        # 第一次创建索引图
        color_stats1 = {'#FF0000': 1, '#00FF00': 2}
        group_status_stats1 = {'current': 1, 'labeled': 2}
        
        visualizer._create_color_index_chart(color_stats1, group_status_stats1)
        first_children_count = len(visualizer.ax_color_legend.get_children())
        print(f"第一次创建后，索引图对象数量: {first_children_count}")
        
        # 第二次创建索引图（不同数据）
        color_stats2 = {'#0000FF': 3, '#FFFF00': 1, '#FF00FF': 2}
        group_status_stats2 = {'current': 2, 'labeled': 1, 'unlabeled': 3}
        
        visualizer._create_color_index_chart(color_stats2, group_status_stats2)
        second_children_count = len(visualizer.ax_color_legend.get_children())
        print(f"第二次创建后，索引图对象数量: {second_children_count}")
        
        # 检查是否正确清除了缓存
        if second_children_count > 0:
            print("✅ 缓存清除测试通过：索引图已更新为新内容")
        else:
            print("❌ 缓存清除测试失败：索引图没有内容")
            
    except Exception as e:
        print(f"❌ 缓存清除测试失败: {e}")

if __name__ == "__main__":
    print("🎨 索引图修复测试程序")
    print("=" * 50)
    
    # 运行缓存清除测试
    test_cache_clearing()
    
    print("\n" + "=" * 50)
    print("🖼️ 启动可视化测试...")
    
    # 运行可视化测试
    test_legend_fixes()
