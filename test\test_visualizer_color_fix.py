#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可视化器颜色修复效果
验证调试输出和视图输出是否使用相同的颜色逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_visualizer_color_fix():
    """测试可视化器颜色修复效果"""
    print("🔧 测试可视化器颜色修复效果")
    print("=" * 80)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        # 创建应用
        root = tk.Tk()
        root.withdraw()
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用创建完成")
        
        # 创建测试数据
        test_entities = [
            {'id': 1, 'type': 'LINE', 'layer': 'A-WALL', 'start': [0, 0], 'end': [100, 0]},
            {'id': 2, 'type': 'LINE', 'layer': 'A-WINDOW', 'start': [20, 0], 'end': [40, 0]},
            {'id': 3, 'type': 'LINE', 'layer': 'FURNITURE', 'start': [30, 30], 'end': [70, 30]},
            {'id': 4, 'type': 'LINE', 'layer': '0', 'start': [10, 10], 'end': [90, 10]},
        ]
        
        # 创建分组
        wall_group = [test_entities[0]]
        door_window_group = [test_entities[1]]
        furniture_group = [test_entities[2]]
        other_group = [test_entities[3]]
        all_groups = [wall_group, door_window_group, furniture_group, other_group]
        
        # 创建组状态信息
        groups_info = [
            {
                'index': 0,
                'label': 'wall',
                'status': 'auto_labeled',
                'display_color': '#8B4513'
            },
            {
                'index': 1,
                'label': 'door_window',
                'status': 'auto_labeled',
                'display_color': '#87CEEB'
            },
            {
                'index': 2,
                'label': 'furniture',
                'status': 'labeled',
                'display_color': '#0000FF'
            },
            {
                'index': 3,
                'label': 'other',
                'status': 'unlabeled',
                'display_color': '#D3D3D3'
            }
        ]
        
        # 设置处理器数据
        app.processor.current_file_entities = test_entities
        app.processor.all_groups = all_groups
        app.processor.groups_info = groups_info
        app.processor.auto_labeled_entities = [test_entities[0], test_entities[1]]
        app.processor.labeled_entities = [test_entities[2]]
        
        visualizer = app.visualizer
        
        print(f"\n🔍 测试场景:")
        print(f"   墙体组: 自动标注 - 期望颜色 #8B4513")
        print(f"   门窗组: 自动标注 - 期望颜色 #87CEEB")
        print(f"   家具组: 手动标注 - 期望颜色 #0000FF")
        print(f"   其他组: 未标注 - 期望颜色 #D3D3D3")
        
        # 测试1：draw_entities方法
        print(f"\n🎨 测试1: draw_entities方法")
        print("-" * 60)
        
        try:
            print("调用 draw_entities 方法...")
            visualizer.draw_entities(
                test_entities,
                labeled_entities=app.processor.labeled_entities,
                current_group_entities=[],
                all_groups=all_groups,
                groups_info=groups_info,
                processor=app.processor
            )
            print("✅ draw_entities 方法调用成功")
        except Exception as e:
            print(f"❌ draw_entities 方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试2：draw_groups方法
        print(f"\n🎨 测试2: draw_groups方法")
        print("-" * 60)
        
        try:
            print("调用 draw_groups 方法...")
            visualizer.draw_groups(
                all_groups,
                groups_info=groups_info,
                labeled_entities=app.processor.labeled_entities,
                processor=app.processor
            )
            print("✅ draw_groups 方法调用成功")
        except Exception as e:
            print(f"❌ draw_groups 方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试3：visualize_entity_group方法
        print(f"\n🎨 测试3: visualize_entity_group方法")
        print("-" * 60)
        
        try:
            print("调用 visualize_entity_group 方法...")
            visualizer.visualize_entity_group(
                furniture_group,
                category_mapping={},
                labeled_entities=app.processor.labeled_entities,
                current_group_entities=[],
                all_groups=all_groups,
                groups_info=groups_info,
                processor=app.processor
            )
            print("✅ visualize_entity_group 方法调用成功")
        except Exception as e:
            print(f"❌ visualize_entity_group 方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试4：visualize_overview方法
        print(f"\n🎨 测试4: visualize_overview方法")
        print("-" * 60)
        
        try:
            print("调用 visualize_overview 方法...")
            visualizer.visualize_overview(
                test_entities,
                current_group_entities=[],
                labeled_entities=app.processor.labeled_entities,
                processor=app.processor,
                all_groups=all_groups,
                groups_info=groups_info
            )
            print("✅ visualize_overview 方法调用成功")
        except Exception as e:
            print(f"❌ visualize_overview 方法调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试5：颜色一致性验证
        print(f"\n🔍 测试5: 颜色一致性验证")
        print("-" * 60)
        
        color_consistency_results = []
        
        for group_idx, (group, group_info) in enumerate(zip(all_groups, groups_info)):
            entity = group[0]
            
            print(f"\n组{group_idx+1} ({group_info['label']}) - 状态: {group_info['status']}")
            print(f"   实体: ID {entity['id']}, 图层 {entity['layer']}")
            print(f"   期望颜色: {group_info['display_color']}")
            
            try:
                # 获取调试输出颜色
                debug_color, debug_alpha, debug_linewidth, debug_status = visualizer._get_entity_display_info_enhanced(
                    entity,
                    labeled_entities=app.processor.labeled_entities,
                    current_group_entities=[],
                    all_groups=all_groups,
                    groups_info=groups_info,
                    processor=app.processor
                )
                
                print(f"   🔍 调试输出: {debug_color} ({debug_status})")
                
                # 检查颜色是否符合期望
                expected_color = group_info['display_color']
                colors_match = debug_color == expected_color
                
                if colors_match:
                    print(f"   ✅ 颜色符合期望")
                else:
                    print(f"   ❌ 颜色不符合期望")
                
                color_consistency_results.append({
                    'group_index': group_idx,
                    'group_label': group_info['label'],
                    'expected_color': expected_color,
                    'actual_color': debug_color,
                    'colors_match': colors_match
                })
                
            except Exception as e:
                print(f"   ❌ 颜色获取失败: {e}")
                color_consistency_results.append({
                    'group_index': group_idx,
                    'group_label': group_info['label'],
                    'expected_color': group_info['display_color'],
                    'actual_color': 'ERROR',
                    'colors_match': False
                })
        
        # 统计结果
        print(f"\n📊 颜色一致性统计")
        print("=" * 80)
        
        total_tests = len(color_consistency_results)
        successful_tests = sum(1 for r in color_consistency_results if r['colors_match'])
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        print(f"\n详细结果:")
        for result in color_consistency_results:
            status = "✅" if result['colors_match'] else "❌"
            print(f"   {status} 组{result['group_index']+1} ({result['group_label']}): 期望={result['expected_color']}, 实际={result['actual_color']}")
        
        # 修复效果评估
        print(f"\n🎯 修复效果评估")
        print("=" * 80)
        
        if successful_tests == total_tests:
            print(f"🎉 完美修复！所有可视化方法都使用了正确的颜色逻辑")
            print(f"   - 调试输出和视图输出现在完全一致")
            print(f"   - 所有实体颜色都符合程序逻辑")
            print(f"   - 配色方案正确应用到视图渲染")
        elif successful_tests >= total_tests * 0.75:
            print(f"✅ 修复基本成功！大部分颜色显示正确")
            print(f"   - {successful_tests}/{total_tests} 个测试通过")
            print(f"   - 需要进一步优化少数不一致的情况")
        else:
            print(f"⚠️ 修复部分成功，仍需改进")
            print(f"   - 只有 {successful_tests}/{total_tests} 个测试通过")
            print(f"   - 需要检查颜色获取逻辑")
        
        # 清理
        root.destroy()
        return successful_tests, total_tests
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        try:
            root.destroy()
        except:
            pass
        return 0, 0

if __name__ == "__main__":
    print("🧪 开始测试可视化器颜色修复效果")
    
    successful, total = test_visualizer_color_fix()
    
    print(f"\n📊 最终测试结果:")
    print(f"   成功率: {successful}/{total} ({successful/total*100 if total > 0 else 0:.1f}%)")
    
    if successful == total and total > 0:
        print(f"\n🎉 可视化器颜色修复完全成功！")
        print(f"   现在调试输出和视图输出使用相同的颜色逻辑")
        print(f"   用户看到的颜色与程序逻辑完全一致")
    elif successful > 0:
        print(f"\n✅ 可视化器颜色修复基本成功")
        print(f"   大部分情况下颜色显示正确")
    else:
        print(f"\n🔧 可视化器颜色修复需要进一步改进")
    
    print(f"\n🚀 测试完成！")
