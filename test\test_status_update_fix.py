#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
状态更新回调修复测试
测试修复后的数据解包逻辑
"""

import os
import sys
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_status_update_data_unpacking():
    """测试状态更新数据解包修复"""
    print("🔍 测试状态更新数据解包修复")
    print("=" * 60)
    
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        print("✅ 主程序导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口，只进行逻辑测试
        
        app = EnhancedCADAppV2(root)
        print("✅ 应用实例创建成功")
        
        # 模拟处理器
        class MockProcessor:
            def __init__(self):
                self.processor = MockCategoryProcessor()
                self.visualizer = MockVisualizer()
                self.current_file_entities = []
                self.auto_labeled_entities = []
                self.labeled_entities = []
        
        class MockCategoryProcessor:
            def __init__(self):
                self.category_mapping = {
                    'wall': '墙体',
                    'door_window': '门窗',
                    'other': '其他'
                }
        
        class MockVisualizer:
            def visualize_overview(self, *args, **kwargs):
                print(f"    📊 可视化调用成功")
                return True
            
            def update_canvas(self, canvas):
                print(f"    🖼️ 画布更新成功")
                return True
        
        # 设置模拟处理器
        app.processor = MockProcessor()
        app.canvas = "mock_canvas"
        
        print("✅ 模拟环境设置完成")
        
        # 测试不同格式的数据
        test_cases = [
            # 正常情况：2个元素的元组
            ("group_labeled", (1, "wall"), "正常2元素元组"),
            
            # 异常情况：3个元素的元组（导致原错误）
            ("group_labeled", (1, "wall", "extra_data"), "3元素元组（原错误情况）"),
            
            # 异常情况：4个元素的元组
            ("group_relabeled", (2, "door_window", "extra1", "extra2"), "4元素元组"),
            
            # 边界情况：1个元素的元组
            ("group_labeled", (1,), "1元素元组"),
            
            # 边界情况：单个值（非元组）
            ("group_relabeled", 3, "单个值"),
            
            # 边界情况：空元组
            ("group_labeled", (), "空元组"),
            
            # 边界情况：None
            ("group_relabeled", None, "None值"),
            
            # 正常情况：manual_complete
            ("manual_complete", None, "手动完成状态"),
        ]
        
        print("\n📋 测试不同数据格式:")
        success_count = 0
        
        for i, (status_type, data, description) in enumerate(test_cases):
            print(f"\n  测试 {i+1}: {description}")
            print(f"    状态类型: {status_type}")
            print(f"    数据内容: {data}")
            print(f"    数据类型: {type(data)}")
            
            try:
                # 调用状态更新方法
                app.on_status_update(status_type, data)
                print(f"    ✅ 处理成功")
                success_count += 1
                
            except Exception as e:
                print(f"    ❌ 处理失败: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个测试用例成功")
        
        # 清理
        root.destroy()
        
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_safe_extract_data_function():
    """测试安全数据提取函数"""
    print("\n🔧 测试安全数据提取函数")
    print("=" * 60)
    
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import tkinter as tk
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 提取safe_extract_data函数进行单独测试
        def safe_extract_data(data, expected_count=2):
            """安全提取数据，处理不同长度的元组"""
            try:
                if isinstance(data, (tuple, list)):
                    if len(data) >= expected_count:
                        return data[:expected_count]  # 只取前面需要的部分
                    else:
                        # 数据不足，用默认值补充
                        result = list(data) + ["未知"] * (expected_count - len(data))
                        return tuple(result[:expected_count])
                else:
                    # 单个值，用默认值补充
                    return (data, "未知") if expected_count == 2 else (data,)
            except Exception as e:
                print(f"⚠️ 数据提取失败: {e}, data={data}")
                return ("未知", "未知") if expected_count == 2 else ("未知",)
        
        # 测试用例
        test_cases = [
            # (输入数据, 期望元素数, 描述)
            ((1, "wall"), 2, "正常2元素元组"),
            ((1, "wall", "extra"), 2, "3元素元组，取前2个"),
            ((1, "wall", "extra1", "extra2"), 2, "4元素元组，取前2个"),
            ((1,), 2, "1元素元组，补充默认值"),
            ((), 2, "空元组，全部补充默认值"),
            (1, 2, "单个值，补充默认值"),
            (None, 2, "None值，补充默认值"),
            ([1, "wall"], 2, "列表格式"),
            ([1, "wall", "extra"], 2, "3元素列表，取前2个"),
        ]
        
        print("📋 测试安全数据提取:")
        all_success = True
        
        for i, (input_data, expected_count, description) in enumerate(test_cases):
            print(f"\n  测试 {i+1}: {description}")
            print(f"    输入: {input_data} ({type(input_data)})")
            
            try:
                result = safe_extract_data(input_data, expected_count)
                print(f"    输出: {result} ({type(result)})")
                print(f"    长度: {len(result)}")
                
                # 验证结果
                if len(result) == expected_count:
                    print(f"    ✅ 长度正确")
                else:
                    print(f"    ❌ 长度错误，期望{expected_count}，实际{len(result)}")
                    all_success = False
                    
            except Exception as e:
                print(f"    ❌ 提取失败: {e}")
                all_success = False
        
        # 清理
        root.destroy()
        
        print(f"\n📊 安全数据提取测试: {'✅ 全部通过' if all_success else '❌ 存在失败'}")
        return all_success
        
    except Exception as e:
        print(f"❌ 安全数据提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 启动状态更新回调修复测试")
    
    success1 = test_status_update_data_unpacking()
    success2 = test_safe_extract_data_function()
    
    if success1 and success2:
        print("\n✅ 所有测试通过，状态更新回调修复应该生效")
        print("\n💡 修复总结:")
        print("1. ✅ 添加了safe_extract_data函数，安全处理不同长度的数据")
        print("2. ✅ 支持2元素、3元素、4元素等不同长度的元组")
        print("3. ✅ 自动截取前面需要的元素，忽略多余数据")
        print("4. ✅ 对不足的数据用默认值补充")
        print("5. ✅ 处理单个值、None值、空元组等边界情况")
        print("6. ✅ 添加了详细的调试信息输出")
        print("\n🎯 现在状态更新应该不再出现:")
        print("   'too many values to unpack (expected 2)'")
        print("\n🔧 修复机制:")
        print("- 检测数据类型和长度")
        print("- 安全截取需要的元素数量")
        print("- 自动补充缺失的默认值")
        print("- 提供详细的错误信息和调试输出")
    else:
        print("\n❌ 测试发现问题，需要进一步修复")
    
    return success1 and success2

if __name__ == "__main__":
    main()
