#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试方法解析顺序（MRO）
检查visualize_overview方法的调用顺序
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_method_resolution():
    """测试方法解析顺序"""
    print("🔍 开始测试方法解析顺序...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("方法解析顺序测试")
        root.geometry("800x600")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 测试1：检查类的继承层次
        print("\n🔍 测试1：检查类的继承层次")
        
        mro = EnhancedCADAppV2.__mro__
        print(f"    方法解析顺序 (MRO):")
        for i, cls in enumerate(mro):
            print(f"      {i+1}. {cls.__name__} ({cls.__module__})")
        
        # 测试2：检查visualize_overview方法的来源
        print("\n🔍 测试2：检查visualize_overview方法的来源")
        
        # 获取方法对象
        method = app.visualize_overview
        print(f"    方法对象: {method}")
        print(f"    方法类型: {type(method)}")
        
        # 检查方法的定义位置
        if hasattr(method, '__func__'):
            func = method.__func__
            print(f"    函数对象: {func}")
            
            if hasattr(func, '__qualname__'):
                print(f"    限定名称: {func.__qualname__}")
            
            if hasattr(func, '__module__'):
                print(f"    定义模块: {func.__module__}")
            
            # 尝试获取源代码位置
            try:
                import inspect
                source_file = inspect.getfile(func)
                source_lines = inspect.getsourcelines(func)
                print(f"    源文件: {source_file}")
                print(f"    起始行号: {source_lines[1]}")
                print(f"    代码行数: {len(source_lines[0])}")
                
                # 显示前几行代码
                print(f"    前3行代码:")
                for i, line in enumerate(source_lines[0][:3]):
                    print(f"      {source_lines[1] + i}: {line.rstrip()}")
                    
            except Exception as e:
                print(f"    ❌ 无法获取源代码信息: {e}")
        
        # 测试3：检查每个父类中的visualize_overview方法
        print("\n🔍 测试3：检查每个父类中的visualize_overview方法")
        
        for cls in mro:
            if hasattr(cls, 'visualize_overview'):
                method_in_cls = getattr(cls, 'visualize_overview')
                print(f"    {cls.__name__}: 有visualize_overview方法")
                
                # 检查是否为实例方法
                if callable(method_in_cls):
                    try:
                        import inspect
                        if hasattr(method_in_cls, '__func__'):
                            func = method_in_cls.__func__
                        else:
                            func = method_in_cls
                        
                        source_file = inspect.getfile(func)
                        source_lines = inspect.getsourcelines(func)
                        print(f"      源文件: {os.path.basename(source_file)}")
                        print(f"      行号: {source_lines[1]}")
                        
                        # 显示方法签名
                        sig = inspect.signature(func)
                        print(f"      签名: {func.__name__}{sig}")
                        
                    except Exception as e:
                        print(f"      ❌ 无法获取详细信息: {e}")
            else:
                print(f"    {cls.__name__}: 没有visualize_overview方法")
        
        # 测试4：直接调用不同类的方法
        print("\n🔍 测试4：直接调用不同类的方法")
        
        # 检查当前实例使用的方法
        current_method = app.visualize_overview
        print(f"    当前实例方法: {current_method}")
        
        # 检查方法的__self__属性
        if hasattr(current_method, '__self__'):
            print(f"    绑定对象: {current_method.__self__}")
            print(f"    绑定对象类型: {type(current_method.__self__)}")
        
        # 测试5：检查方法调用路径
        print("\n🔍 测试5：检查方法调用路径")
        
        # 创建一个简单的测试来跟踪方法调用
        original_method = app.visualize_overview
        
        def traced_visualize_overview(*args, **kwargs):
            print(f"    📞 visualize_overview 被调用")
            print(f"      参数数量: {len(args)}")
            print(f"      关键字参数: {list(kwargs.keys())}")
            
            # 获取调用栈
            import traceback
            stack = traceback.extract_stack()
            print(f"      调用栈（最后3层）:")
            for frame in stack[-4:-1]:  # 排除当前函数
                print(f"        {frame.filename}:{frame.lineno} in {frame.name}")
            
            # 调用原始方法
            return original_method(*args, **kwargs)
        
        # 临时替换方法
        app.visualize_overview = traced_visualize_overview
        
        # 测试调用
        try:
            print(f"    测试调用 visualize_overview...")
            app.visualize_overview([], None, None, None)
            print(f"    ✅ 方法调用成功")
        except Exception as e:
            print(f"    ❌ 方法调用失败: {e}")
        finally:
            # 恢复原始方法
            app.visualize_overview = original_method
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 方法解析顺序测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_method_resolution()
    if success:
        print("\n🎊 方法解析顺序测试成功！")
    else:
        print("\n⚠️ 方法解析顺序测试发现问题")
