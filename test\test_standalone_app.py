#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试独立CAD应用程序
验证融合后的程序是否正常工作
"""

def test_standalone_imports():
    """测试独立程序的导入"""
    print("🔍 测试独立程序导入")
    print("="*50)
    
    try:
        from main_enhanced_standalone import StandaloneCADProcessor, StandaloneCADApp
        print("✅ 主要类导入成功")
        
        # 测试处理器创建
        processor = StandaloneCADProcessor()
        print("✅ 处理器创建成功")
        
        # 测试基本属性
        required_attrs = [
            'current_file_entities', 'all_groups', 'labeled_entities',
            'auto_labeled_entities', 'manual_grouping_mode', 'category_mapping'
        ]
        
        for attr in required_attrs:
            if hasattr(processor, attr):
                print(f"✅ {attr}: 存在")
            else:
                print(f"❌ {attr}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_standalone_app_creation():
    """测试独立应用程序创建"""
    print(f"\n🔍 测试独立应用程序创建")
    print("="*50)
    
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp
        
        # 创建主窗口（不显示）
        root = tk.Tk()
        root.withdraw()
        
        # 创建应用程序
        app = StandaloneCADApp(root)
        print("✅ 应用程序创建成功")
        
        # 测试基本属性
        required_attrs = [
            'processor', 'visualizer', 'canvas', 'current_file',
            'entities', 'groups', 'folder_var', 'status_var'
        ]
        
        for attr in required_attrs:
            if hasattr(app, attr):
                print(f"✅ {attr}: 存在")
            else:
                print(f"❌ {attr}: 缺失")
        
        # 测试GUI组件
        gui_components = [
            'group_tree', 'start_btn', 'stop_btn', 'label_btn',
            'skip_btn', 'prev_btn', 'next_btn'
        ]
        
        for component in gui_components:
            if hasattr(app, component):
                print(f"✅ GUI组件 {component}: 存在")
            else:
                print(f"❌ GUI组件 {component}: 缺失")
        
        # 测试调试功能
        if hasattr(app, '_debug_color_display'):
            print("✅ 调试功能: 存在")
            
            # 测试调试功能执行
            try:
                app._debug_color_display()
                print("✅ 调试功能执行: 成功")
            except Exception as e:
                print(f"⚠️ 调试功能执行: 失败 - {e}")
        else:
            print("❌ 调试功能: 缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 应用程序创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_processor_functionality():
    """测试处理器功能"""
    print(f"\n🔍 测试处理器功能")
    print("="*50)
    
    try:
        from main_enhanced_standalone import StandaloneCADProcessor
        
        # 创建处理器
        processor = StandaloneCADProcessor()
        
        # 创建测试数据
        test_entities = [
            {
                'id': 1,
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': None,
                'auto_labeled': False,
                'points': [(0, 0), (100, 0)]
            },
            {
                'id': 2,
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'label': None,
                'auto_labeled': False,
                'points': [(50, 0), (50, 50)]
            },
            {
                'id': 3,
                'type': 'CIRCLE',
                'layer': 'A-FURNITURE',
                'label': None,
                'auto_labeled': False,
                'points': [(25, 25)],
                'radius': 10
            }
        ]
        
        print(f"✅ 创建测试数据: {len(test_entities)} 个实体")
        
        # 测试基础处理
        processor.current_file_entities = test_entities
        success = processor._process_with_basic_processor(test_entities)
        
        if success:
            print("✅ 基础处理: 成功")
            print(f"  生成组数: {len(processor.all_groups)}")
            print(f"  自动标注实体数: {len(processor.auto_labeled_entities)}")
        else:
            print("❌ 基础处理: 失败")
        
        # 测试组信息更新
        processor._update_groups_info()
        print(f"✅ 组信息更新: {len(processor.groups_info)} 个组")
        
        # 测试标注功能
        if processor.all_groups:
            processor.current_group_index = 0
            processor.current_group_entities = processor.all_groups[0]
            
            success = processor.label_current_group('wall')
            if success:
                print("✅ 标注功能: 成功")
                print(f"  已标注实体数: {len(processor.labeled_entities)}")
            else:
                print("❌ 标注功能: 失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_components():
    """测试与其他组件的集成"""
    print(f"\n🔍 测试组件集成")
    print("="*50)
    
    # 测试各个组件的可用性
    components = [
        ('CADDataProcessor', 'cad_data_processor'),
        ('CADVisualizer', 'cad_visualizer'),
        ('WallFillProcessorV2', 'wall_fill_processor_v2'),
        ('OverviewCacheSystem', 'overview_cache_system'),
        ('ShadowSystem', 'shadow_system'),
        ('LineModeManager', 'line_mode_manager'),
        ('LogExporter', 'log_exporter'),
        ('EnhancedCADProcessor', 'enhanced_cad_processor')
    ]
    
    available_count = 0
    total_count = len(components)
    
    for component_name, module_name in components:
        try:
            __import__(module_name)
            print(f"✅ {component_name}: 可用")
            available_count += 1
        except ImportError:
            print(f"⚠️ {component_name}: 不可用")
    
    print(f"\n📊 组件可用性: {available_count}/{total_count} ({available_count/total_count*100:.1f}%)")
    
    if available_count >= total_count * 0.5:
        print("✅ 组件集成: 良好")
        return True
    else:
        print("⚠️ 组件集成: 部分功能可能受限")
        return False

def main():
    """主函数"""
    print("🚀 启动独立CAD应用程序测试")
    print("="*60)
    
    # 测试1：导入测试
    test1_result = test_standalone_imports()
    
    # 测试2：应用程序创建测试
    test2_result = test_standalone_app_creation()
    
    # 测试3：处理器功能测试
    test3_result = test_processor_functionality()
    
    # 测试4：组件集成测试
    test4_result = test_integration_with_components()
    
    print(f"\n" + "="*60)
    print("📊 独立CAD应用程序测试结果")
    print("="*60)
    
    print(f"🧪 导入测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"🧪 应用程序创建: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"🧪 处理器功能: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"🧪 组件集成: {'✅ 通过' if test4_result else '⚠️ 部分通过'}")
    
    overall_success = test1_result and test2_result and test3_result
    
    if overall_success:
        print(f"\n🎉 独立程序测试通过！")
        print(f"\n💡 使用说明:")
        print(f"1. 启动程序: python main_enhanced_standalone.py")
        print(f"2. 选择包含DXF文件的文件夹")
        print(f"3. 点击'开始处理'按钮")
        print(f"4. 使用类别按钮标注各个组")
        print(f"5. 按F12键或Ctrl+D键进行颜色调试")
        print(f"6. 完成后导出数据集")
        
        print(f"\n🔧 调试功能:")
        print(f"- F12键: 触发颜色调试")
        print(f"- Ctrl+D: 触发颜色调试")
        print(f"- 调试按钮: 手动触发调试")
        print(f"- 自动调试: 首次加载完成后自动触发")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
