#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接测试wall.dxf文件的内容和颜色显示
不依赖GUI界面，直接分析文件和测试颜色逻辑
"""

import os
import sys

def analyze_wall_dxf():
    """分析wall.dxf文件内容"""
    print("🔍 分析wall.dxf文件内容")
    print("="*60)
    
    try:
        dxf_file_path = r"C:\A-BCXM\cad文件\cs-wall\wall.dxf"
        
        if not os.path.exists(dxf_file_path):
            print(f"❌ 文件不存在: {dxf_file_path}")
            return False
        
        print(f"✅ 文件存在: {dxf_file_path}")
        print(f"📊 文件大小: {os.path.getsize(dxf_file_path)} 字节")
        
        # 读取DXF文件内容
        try:
            import ezdxf
            doc = ezdxf.readfile(dxf_file_path)
            msp = doc.modelspace()
            
            print(f"✅ DXF文件读取成功")
            
            # 统计实体
            entities = list(msp)
            print(f"📊 总实体数: {len(entities)}")
            
            # 统计实体类型
            entity_types = {}
            layer_stats = {}
            
            for entity in entities:
                entity_type = entity.dxftype()
                layer = entity.dxf.layer
                
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                layer_stats[layer] = layer_stats.get(layer, 0) + 1
            
            print(f"📊 实体类型分布:")
            for entity_type, count in entity_types.items():
                print(f"  {entity_type}: {count} 个")
            
            print(f"📊 图层分布:")
            for layer, count in layer_stats.items():
                print(f"  {layer}: {count} 个")
            
            return entities
            
        except Exception as e:
            print(f"❌ DXF文件读取失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 文件分析失败: {e}")
        return False

def test_color_logic_with_wall_entities(entities):
    """使用wall.dxf的实体测试颜色逻辑"""
    print("\n🎨 测试颜色逻辑")
    print("="*60)
    
    try:
        # 导入可视化器
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        print("✅ 可视化器初始化成功")
        
        # 转换ezdxf实体为内部格式
        converted_entities = []
        
        for i, entity in enumerate(entities[:10]):  # 只测试前10个实体
            try:
                # 转换实体格式
                converted_entity = {
                    'id': i + 1,
                    'type': entity.dxftype(),
                    'layer': entity.dxf.layer,
                    'label': None,
                    'auto_labeled': False
                }
                
                # 添加坐标信息
                if hasattr(entity, 'dxf') and hasattr(entity.dxf, 'start'):
                    converted_entity['points'] = [(entity.dxf.start.x, entity.dxf.start.y), (entity.dxf.end.x, entity.dxf.end.y)]
                elif hasattr(entity, 'dxf') and hasattr(entity.dxf, 'center'):
                    converted_entity['points'] = [(entity.dxf.center.x, entity.dxf.center.y)]
                    if hasattr(entity.dxf, 'radius'):
                        converted_entity['radius'] = entity.dxf.radius
                else:
                    converted_entity['points'] = [(0, 0)]
                
                converted_entities.append(converted_entity)
                
            except Exception as e:
                print(f"⚠️ 实体{i+1}转换失败: {e}")
        
        print(f"✅ 转换了 {len(converted_entities)} 个实体")
        
        # 模拟分组
        print("\n🔍 模拟分组...")
        
        # 按图层分组
        groups = {}
        for entity in converted_entities:
            layer = entity['layer']
            if layer not in groups:
                groups[layer] = []
            groups[layer].append(entity)
        
        print(f"📊 分组结果: {len(groups)} 个组")
        for layer, group_entities in groups.items():
            print(f"  {layer}: {len(group_entities)} 个实体")
        
        # 测试每个组的颜色显示
        print("\n🎨 测试各组颜色显示...")
        
        for group_name, group_entities in groups.items():
            print(f"\n🔍 测试组: {group_name}")
            
            # 测试待标注状态（当前组）
            print(f"  📊 待标注状态（当前组）:")
            for i, entity in enumerate(group_entities[:3]):  # 只测试前3个
                try:
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=[],
                        current_group_entities=group_entities,
                        processor=None
                    )
                    
                    print(f"    实体{i+1} ({entity['type']}, {entity['layer']}):")
                    print(f"      颜色: {color}")
                    print(f"      透明度: {alpha}")
                    print(f"      状态: {status}")
                    
                    # 检查是否使用了墙体颜色
                    if color == '#8B4513':
                        print(f"      ⚠️ 发现问题：使用了墙体颜色 #8B4513")
                        
                        # 详细诊断
                        print(f"      🔍 详细诊断:")
                        backup_color = visualizer._get_entity_color_by_type_and_layer(entity)
                        print(f"        备用颜色方案: {backup_color}")
                        
                        layer_name = str(entity.get('layer', '')).lower()
                        print(f"        图层名称: '{layer_name}'")
                        
                        if 'wall' in layer_name:
                            print(f"        ❌ 问题原因：图层名包含'wall'，被误判为墙体")
                        
                    elif color == '#FF0000':
                        print(f"      ✅ 正确：使用了红色高亮")
                    else:
                        print(f"      ℹ️ 使用了其他颜色")
                        
                except Exception as e:
                    print(f"    ❌ 实体{i+1}颜色测试失败: {e}")
            
            # 测试手动标注为家具后的状态
            print(f"  📊 手动标注为家具后:")
            
            # 更新实体标签
            for entity in group_entities:
                entity['label'] = 'furniture'
                entity['auto_labeled'] = False
            
            for i, entity in enumerate(group_entities[:3]):  # 只测试前3个
                try:
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=group_entities,  # 标注后加入已标注列表
                        current_group_entities=group_entities,  # 仍然是当前组
                        processor=None
                    )
                    
                    print(f"    实体{i+1} ({entity['type']}, {entity['layer']}):")
                    print(f"      标签: {entity.get('label', 'None')}")
                    print(f"      颜色: {color}")
                    print(f"      状态: {status}")
                    
                    # 验证标注后的颜色
                    if status == 'current' and color == '#FF0000':
                        print(f"      ✅ 当前组高亮正确")
                    elif status == 'labeled' and color != '#808080':
                        print(f"      ✅ 标注后分类颜色正确")
                    else:
                        print(f"      ⚠️ 颜色状态需要检查")
                        
                except Exception as e:
                    print(f"    ❌ 实体{i+1}标注后颜色测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_color_issue():
    """测试特定的颜色问题"""
    print("\n🔍 测试特定颜色问题")
    print("="*60)
    
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        # 创建测试实体，模拟wall.dxf中可能的实体
        test_entities = [
            # 墙体图层实体
            {'id': 1, 'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (100, 0)], 'label': None, 'auto_labeled': False},
            {'id': 2, 'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': None, 'auto_labeled': False},
            {'id': 3, 'type': 'LINE', 'layer': '墙体', 'points': [(100, 100), (0, 100)], 'label': None, 'auto_labeled': False},
            
            # 其他图层实体
            {'id': 4, 'type': 'LINE', 'layer': 'DOOR', 'points': [(50, 0), (60, 0)], 'label': None, 'auto_labeled': False},
            {'id': 5, 'type': 'CIRCLE', 'layer': 'FURNITURE', 'points': [(50, 50)], 'label': None, 'auto_labeled': False},
        ]
        
        print("🧪 测试不同图层实体的颜色:")
        
        for entity in test_entities:
            print(f"\n  实体: {entity['type']} - {entity['layer']}")
            
            # 测试作为当前组时的颜色
            try:
                color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                    entity,
                    labeled_entities=[],
                    current_group_entities=[entity],
                    processor=None
                )
                
                print(f"    当前组状态:")
                print(f"      颜色: {color}")
                print(f"      状态: {status}")
                
                if color == '#8B4513':
                    print(f"      ⚠️ 问题：使用了墙体颜色")
                    
                    # 测试备用颜色方案
                    backup_color = visualizer._get_entity_color_by_type_and_layer(entity)
                    print(f"      备用颜色: {backup_color}")
                    
                    # 分析原因
                    layer_name = str(entity.get('layer', '')).lower()
                    if 'wall' in layer_name or '墙' in layer_name:
                        print(f"      原因: 图层名包含'wall'或'墙'")
                elif color == '#FF0000':
                    print(f"      ✅ 正确：红色高亮")
                
            except Exception as e:
                print(f"    ❌ 颜色测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定颜色问题测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动wall.dxf直接测试")
    print("="*80)
    
    # 分析文件内容
    entities = analyze_wall_dxf()
    
    if entities:
        # 测试颜色逻辑
        test_color_logic_with_wall_entities(entities)
    
    # 测试特定颜色问题
    test_specific_color_issue()
    
    print("\n" + "="*80)
    print("📊 测试完成")
    print("="*80)

if __name__ == "__main__":
    main()
