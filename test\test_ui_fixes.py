#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui_fixes():
    """测试UI修复效果"""
    print("🧪 测试UI修复效果")
    print("=" * 50)
    
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 主程序导入成功")
        
        # 创建应用实例
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        app = EnhancedCADAppV2(root)
        print("✅ 应用实例创建成功")
        
        # 测试1：组列表更新逻辑
        print("\n🔧 测试1：组列表更新逻辑")
        print("-" * 30)
        
        # 创建模拟处理器数据
        if hasattr(app, 'processor') and app.processor:
            # 模拟分组数据
            test_groups = [
                [{'type': 'LINE', 'layer': 'A-WALL', 'label': None}],
                [{'type': 'LINE', 'layer': 'A-WINDOW', 'label': None}],
                [{'type': 'LINE', 'layer': 'A-DOOR', 'label': 'door_window'}],
            ]
            
            app.processor.all_groups = test_groups
            app.processor.manual_grouping_mode = True
            app.processor.current_group_index = 1  # 设置第2组为当前组
            
            # 测试组信息获取
            groups_info = app.get_groups_info()
            print(f"获取到 {len(groups_info)} 个组信息:")
            
            for i, info in enumerate(groups_info):
                print(f"  组{i+1}: 状态={info.get('status')}, 标签={info.get('label')}, 实体数={info.get('entity_count')}")
                
                # 检查是否正确识别标注中的组
                if info.get('status') == 'labeling':
                    print(f"    ✅ 正确识别标注中的组: 组{i+1}")
        
        # 测试2：可视化器对齐
        print("\n🔧 测试2：可视化器对齐")
        print("-" * 30)
        
        if hasattr(app, 'visualizer') and app.visualizer:
            # 检查网格布局配置
            if hasattr(app.visualizer, 'fig'):
                print("✅ 可视化器图形对象存在")
                
                # 检查子图配置
                axes = app.visualizer.fig.get_axes()
                print(f"  子图数量: {len(axes)}")
                
                for i, ax in enumerate(axes):
                    pos = ax.get_position()
                    print(f"  子图{i+1}: 位置=({pos.x0:.2f}, {pos.y0:.2f}, {pos.width:.2f}, {pos.height:.2f})")
        
        # 测试3：隐藏/显示左侧面板功能
        print("\n🔧 测试3：隐藏/显示左侧面板功能")
        print("-" * 30)
        
        if hasattr(app, '_hide_left_panel') and hasattr(app, '_show_left_panel'):
            print("✅ 隐藏/显示左侧面板方法存在")
            
            # 检查相关属性
            if hasattr(app, 'detail_frame'):
                print("  ✅ detail_frame 存在")
            if hasattr(app, 'overview_frame'):
                print("  ✅ overview_frame 存在")
            if hasattr(app, 'left_panel_visible'):
                print(f"  当前左侧面板状态: {'显示' if app.left_panel_visible else '隐藏'}")
        
        print("\n✅ 所有UI修复测试完成")
        
        # 清理资源
        root.destroy()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui_fixes()
