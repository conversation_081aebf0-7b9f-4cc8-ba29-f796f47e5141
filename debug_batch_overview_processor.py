#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试批量概览处理器参数传递
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_processor_groups():
    """调试处理器组信息"""
    
    print("🔍 调试处理器组信息传递")
    print("=" * 50)
    
    try:
        # 模拟真实的处理器
        class MockProcessor:
            def __init__(self, has_groups=True):
                self.current_file_entities = []
                for i in range(455):  # 455个实体
                    entity = {
                        'type': 'LINE',
                        'layer': f'Layer_{i % 10}',
                        'points': [[i*0.1, 0], [(i+1)*0.1, 0]],
                        'id': f'entity_{i}'
                    }
                    self.current_file_entities.append(entity)
                
                if has_groups:
                    # 模拟有组信息的情况
                    self.all_groups = [
                        self.current_file_entities[0:150],   # 组1：墙体
                        self.current_file_entities[150:300], # 组2：门窗  
                        self.current_file_entities[300:455]  # 组3：未标注
                    ]
                    self.groups_info = [
                        {'status': 'labeled', 'label': 'wall', 'is_current_group': False},
                        {'status': 'labeled', 'label': 'door_window', 'is_current_group': True},
                        {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}
                    ]
                    print(f"✅ 创建有组信息的处理器: {len(self.all_groups)}个组")
                else:
                    # 模拟无组信息的情况
                    print("⚠️ 创建无组信息的处理器")
                
                self.labeled_entities = []
                self.auto_labeled_entities = []
        
        # 测试场景1：有组信息的处理器
        print("\n🧪 测试场景1：有组信息的处理器")
        processor_with_groups = MockProcessor(has_groups=True)
        
        print(f"  处理器属性检查:")
        print(f"    hasattr(processor, 'all_groups'): {hasattr(processor_with_groups, 'all_groups')}")
        print(f"    hasattr(processor, 'groups_info'): {hasattr(processor_with_groups, 'groups_info')}")
        print(f"    len(all_groups): {len(processor_with_groups.all_groups)}")
        print(f"    len(groups_info): {len(processor_with_groups.groups_info)}")
        
        # 模拟批量概览中的组信息获取
        all_groups = getattr(processor_with_groups, 'all_groups', [])
        groups_info = getattr(processor_with_groups, 'groups_info', [])
        
        print(f"  批量概览获取的组信息:")
        print(f"    all_groups: {len(all_groups)}个组")
        print(f"    groups_info: {len(groups_info)}个组信息")
        
        # 测试实体在组中的查找
        test_entity = processor_with_groups.current_file_entities[100]  # 第100个实体，应该在第1组
        entity_id = id(test_entity)
        
        found_group = None
        found_group_info = None
        
        for group_index, group in enumerate(all_groups):
            if group and any(id(e) == entity_id for e in group):
                found_group = group_index
                if group_index < len(groups_info):
                    found_group_info = groups_info[group_index]
                break
        
        print(f"  实体查找测试:")
        print(f"    测试实体索引: 100")
        print(f"    找到的组: {found_group}")
        print(f"    组信息: {found_group_info}")
        
        # 测试场景2：无组信息的处理器
        print("\n🧪 测试场景2：无组信息的处理器")
        processor_no_groups = MockProcessor(has_groups=False)
        
        print(f"  处理器属性检查:")
        print(f"    hasattr(processor, 'all_groups'): {hasattr(processor_no_groups, 'all_groups')}")
        print(f"    hasattr(processor, 'groups_info'): {hasattr(processor_no_groups, 'groups_info')}")
        
        # 模拟批量概览中的组信息获取
        all_groups_empty = getattr(processor_no_groups, 'all_groups', [])
        groups_info_empty = getattr(processor_no_groups, 'groups_info', [])
        
        print(f"  批量概览获取的组信息:")
        print(f"    all_groups: {len(all_groups_empty)}个组")
        print(f"    groups_info: {len(groups_info_empty)}个组信息")
        
        # 测试场景3：None处理器
        print("\n🧪 测试场景3：None处理器")
        processor_none = None
        
        all_groups_none = getattr(processor_none, 'all_groups', []) if processor_none else []
        groups_info_none = getattr(processor_none, 'groups_info', []) if processor_none else []
        
        print(f"  批量概览获取的组信息:")
        print(f"    all_groups: {len(all_groups_none)}个组")
        print(f"    groups_info: {len(groups_info_none)}个组信息")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_overview_color_logic():
    """测试批量概览颜色逻辑"""
    
    print("\n🎨 测试批量概览颜色逻辑")
    print("=" * 40)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试数据
        test_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'test_entity'
        }
        
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'unlabeled': '#C0C0C0',
            'other': '#808080'
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
        
        visualizer = MockVisualizer()
        
        # 测试场景：有组信息
        all_groups = [[test_entity]]  # 实体在第1组
        groups_info = [{'status': 'labeled', 'label': 'wall', 'is_current_group': False}]
        
        print("🧪 测试有组信息的颜色获取:")
        color = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        print(f"  获取颜色: {color}")
        print(f"  期望颜色: {color_scheme['wall']} (墙体颜色)")
        
        # 测试场景：无组信息
        print("\n🧪 测试无组信息的颜色获取:")
        color_no_groups = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, visualizer, [], [], [], None
        )
        print(f"  获取颜色: {color_no_groups}")
        print(f"  期望颜色: {color_scheme['unlabeled']} (未标注颜色)")
        
        # 测试场景：实体不在组中
        other_entity = {
            'type': 'LINE',
            'layer': 'A-DOOR',
            'points': [[20, 0], [30, 0]],
            'id': 'other_entity'
        }
        
        print("\n🧪 测试实体不在组中的颜色获取:")
        color_not_in_group = fix._get_entity_color_for_batch_enhanced(
            other_entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        print(f"  获取颜色: {color_not_in_group}")
        print(f"  期望颜色: {color_scheme['unlabeled']} (未标注颜色)")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_real_world_scenario():
    """分析真实世界场景"""
    
    print("\n🌍 分析真实世界场景")
    print("=" * 30)
    
    print("🎯 可能的问题场景:")
    print()
    print("1. **处理器传递正确，但组信息为空**")
    print("   - processor不为None")
    print("   - 但processor.all_groups为空列表[]")
    print("   - 或processor.groups_info为空列表[]")
    print()
    print("2. **组信息存在，但实体不在任何组中**")
    print("   - all_groups有数据")
    print("   - groups_info有数据")
    print("   - 但455个实体都不在这些组中")
    print()
    print("3. **组信息格式不正确**")
    print("   - groups_info中缺少必要字段")
    print("   - 如缺少'status'或'label'字段")
    print()
    print("4. **批量概览触发条件问题**")
    print("   - 455个实体触发了批量概览")
    print("   - 但批量概览的组信息获取有问题")
    print()
    print("💡 解决方案建议:")
    print()
    print("1. **增强调试输出**")
    print("   - 在批量概览中输出详细的组信息")
    print("   - 输出每个实体的颜色获取过程")
    print()
    print("2. **改进回退机制**")
    print("   - 当组信息无效时，强制使用实体属性")
    print("   - 确保至少有基本的颜色显示")
    print()
    print("3. **验证组信息完整性**")
    print("   - 检查all_groups和groups_info的对应关系")
    print("   - 验证实体确实在组中")

if __name__ == "__main__":
    print("🔍 批量概览处理器参数调试程序")
    print("=" * 50)
    
    # 调试处理器组信息
    processor_result = debug_processor_groups()
    
    # 测试颜色逻辑
    color_result = test_batch_overview_color_logic()
    
    # 分析真实场景
    analyze_real_world_scenario()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结:")
    
    if processor_result and color_result:
        print("✅ 处理器组信息传递正常")
        print("✅ 批量概览颜色逻辑正常")
        print("⚠️ 问题可能在于实际使用中的组信息内容")
        print("\n💡 建议：")
        print("  1. 检查实际处理器的all_groups和groups_info内容")
        print("  2. 验证455个实体是否真的在组中")
        print("  3. 添加更详细的调试输出到批量概览方法")
    else:
        print("❌ 部分测试失败")
        if not processor_result:
            print("❌ 处理器组信息传递有问题")
        if not color_result:
            print("❌ 批量概览颜色逻辑有问题")
