#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试布局修复
检查图像控制和图层控制区域是否有足够的显示空间
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_layout_fix():
    """测试布局修复效果"""
    print("🔍 开始测试布局修复效果...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("布局修复测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(2)
        
        # 测试1：检查主容器的权重配置
        print("\n🔍 测试1：检查主容器权重配置")
        
        if hasattr(app, 'main_container'):
            container = app.main_container
            print(f"    主容器: {container}")
            
            # 获取grid配置信息
            try:
                # 检查行权重
                row_weights = []
                for i in range(2):  # 检查前2行
                    weight = container.grid_rowconfigure(i)['weight']
                    row_weights.append(weight)
                    print(f"    第{i}行权重: {weight}")
                
                # 计算权重比例
                total_weight = sum(row_weights)
                if total_weight > 0:
                    for i, weight in enumerate(row_weights):
                        percentage = (weight / total_weight) * 100
                        print(f"    第{i}行占比: {percentage:.1f}%")
                
            except Exception as e:
                print(f"    ❌ 获取权重配置失败: {e}")
        else:
            print(f"    ❌ 主容器不存在")
        
        # 测试2：检查各区域的实际大小
        print("\n🔍 测试2：检查各区域实际大小")
        
        areas = [
            ('detail_frame', '图像预览区'),
            ('overview_frame', '房间填充区'),
            ('zoom_frame', '图像控制区'),
            ('color_frame', '配色系统区')
        ]
        
        for attr_name, area_name in areas:
            if hasattr(app, attr_name):
                frame = getattr(app, attr_name)
                try:
                    width = frame.winfo_width()
                    height = frame.winfo_height()
                    x = frame.winfo_x()
                    y = frame.winfo_y()
                    print(f"    {area_name}: {width}x{height} at ({x}, {y})")
                    
                    # 检查是否太小
                    if height < 100:
                        print(f"      ⚠️ 高度过小: {height}px")
                    elif height < 200:
                        print(f"      ⚠️ 高度较小: {height}px")
                    else:
                        print(f"      ✅ 高度合适: {height}px")
                        
                except Exception as e:
                    print(f"    ❌ 获取{area_name}大小失败: {e}")
            else:
                print(f"    ❌ {area_name}不存在")
        
        # 测试3：检查索引图区域大小
        print("\n🔍 测试3：检查索引图区域大小")
        
        if hasattr(app, 'legend_frame') and app.legend_frame:
            legend_frame = app.legend_frame
            try:
                width = legend_frame.winfo_width()
                height = legend_frame.winfo_height()
                print(f"    索引图区域: {width}x{height}")
                
                if height > 100:
                    print(f"      ⚠️ 索引图高度过大: {height}px，可能挤压其他区域")
                else:
                    print(f"      ✅ 索引图高度合适: {height}px")
                    
            except Exception as e:
                print(f"    ❌ 获取索引图大小失败: {e}")
        else:
            print(f"    ❌ 索引图区域不存在")
        
        # 测试4：检查房间列表容器的权重配置
        print("\n🔍 测试4：检查房间列表容器权重配置")
        
        if hasattr(app, 'room_list_container'):
            container = app.room_list_container
            try:
                # 检查行权重
                row_weights = []
                for i in range(2):  # 检查前2行
                    weight = container.grid_rowconfigure(i)['weight']
                    row_weights.append(weight)
                    print(f"    房间列表容器第{i}行权重: {weight}")
                
                # 计算权重比例
                total_weight = sum(row_weights)
                if total_weight > 0:
                    for i, weight in enumerate(row_weights):
                        percentage = (weight / total_weight) * 100
                        area_name = "房间列表" if i == 0 else "索引图"
                        print(f"    {area_name}占比: {percentage:.1f}%")
                
            except Exception as e:
                print(f"    ❌ 获取房间列表容器权重配置失败: {e}")
        else:
            print(f"    ❌ 房间列表容器不存在")
        
        # 测试5：检查图层控制区域是否可用
        print("\n🔍 测试5：检查图层控制区域")
        
        if hasattr(app, 'layer_control_container'):
            layer_container = app.layer_control_container
            try:
                width = layer_container.winfo_width()
                height = layer_container.winfo_height()
                print(f"    图层控制容器: {width}x{height}")
                
                if height < 150:
                    print(f"      ❌ 图层控制高度不足: {height}px，可能无法正常显示")
                elif height < 250:
                    print(f"      ⚠️ 图层控制高度较小: {height}px")
                else:
                    print(f"      ✅ 图层控制高度充足: {height}px")
                    
            except Exception as e:
                print(f"    ❌ 获取图层控制大小失败: {e}")
        else:
            print(f"    ❌ 图层控制容器不存在")
        
        # 等待一段时间让用户观察
        print("\n⏳ 等待5秒让用户观察界面布局...")
        time.sleep(5)
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 布局修复测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_layout_fix()
    if success:
        print("\n🎊 布局修复测试成功！")
    else:
        print("\n⚠️ 布局修复测试发现问题")
