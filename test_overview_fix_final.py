#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试概览图修复
验证概览图参数错误是否已修复
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_overview_fix_final():
    """最终测试概览图修复"""
    print("🔍 开始最终测试概览图修复...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("概览图修复最终测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(1)
        
        # 测试1：测试修复的无参数调用
        print("\n🔍 测试1：测试修复的无参数调用")
        
        try:
            # 模拟触发刷新视图的情况
            if hasattr(app, '_refresh_view'):
                print("    调用 _refresh_view 方法...")
                app._refresh_view()
                print("    ✅ _refresh_view 调用成功")
            else:
                print("    ⚠️ _refresh_view 方法不存在")
        except Exception as e:
            print(f"    ❌ _refresh_view 调用失败: {e}")
        
        # 测试2：测试直接调用visualize_overview方法
        print("\n🔍 测试2：测试直接调用visualize_overview方法")
        
        try:
            # 创建模拟数据
            mock_entities = [
                {'id': 'test1', 'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (100, 0)]},
                {'id': 'test2', 'type': 'LINE', 'layer': 'DOOR', 'points': [(50, 0), (50, 100)]}
            ]
            
            print("    调用 visualize_overview 方法...")
            app.visualize_overview(
                mock_entities,      # all_entities
                None,               # current_group_entities
                None,               # labeled_entities
                app.processor,      # processor
                None,               # current_group_index
                None,               # wall_fills
                None                # wall_fill_processor
            )
            print("    ✅ visualize_overview 调用成功")
            
        except Exception as e:
            print(f"    ❌ visualize_overview 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试3：测试可视化器的visualize_overview方法
        print("\n🔍 测试3：测试可视化器的visualize_overview方法")
        
        try:
            if hasattr(app, 'visualizer') and app.visualizer:
                print("    调用 visualizer.visualize_overview 方法...")
                app.visualizer.visualize_overview(
                    mock_entities,      # all_entities
                    None,               # current_group_entities
                    None,               # labeled_entities
                    app.processor,      # processor
                    None,               # current_group_index
                    None,               # wall_fills
                    None,               # wall_fill_processor
                    None,               # hidden_groups
                    [],                 # all_groups
                    []                  # groups_info
                )
                print("    ✅ visualizer.visualize_overview 调用成功")
            else:
                print("    ❌ 可视化器不存在")
                
        except Exception as e:
            print(f"    ❌ visualizer.visualize_overview 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试4：测试处理器的可视化器方法
        print("\n🔍 测试4：测试处理器的可视化器方法")
        
        try:
            if hasattr(app, 'processor') and app.processor and hasattr(app.processor, 'visualizer'):
                print("    调用 processor.visualizer.visualize_overview 方法...")
                app.processor.visualizer.visualize_overview(
                    mock_entities,      # all_entities
                    None,               # current_group_entities
                    None,               # labeled_entities
                    app.processor,      # processor
                    None,               # current_group_index
                    None,               # wall_fills
                    None,               # wall_fill_processor
                    None,               # hidden_groups
                    [],                 # all_groups
                    []                  # groups_info
                )
                print("    ✅ processor.visualizer.visualize_overview 调用成功")
            else:
                print("    ❌ 处理器可视化器不存在")
                
        except Exception as e:
            print(f"    ❌ processor.visualizer.visualize_overview 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试5：测试替代可视化器方法
        print("\n🔍 测试5：测试替代可视化器方法")
        
        try:
            if hasattr(app, '_alternative_visualize_overview'):
                print("    调用 _alternative_visualize_overview 方法...")
                app._alternative_visualize_overview(
                    mock_entities,      # all_entities
                    None,               # current_group_entities
                    None,               # labeled_entities
                    app.processor,      # processor
                    None,               # current_group_index
                    None,               # wall_fills
                    None,               # wall_fill_processor
                    None,               # hidden_groups
                    [],                 # all_groups
                    []                  # groups_info
                )
                print("    ✅ _alternative_visualize_overview 调用成功")
            else:
                print("    ❌ _alternative_visualize_overview 方法不存在")
                
        except Exception as e:
            print(f"    ❌ _alternative_visualize_overview 调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 概览图修复最终测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_overview_fix_final()
    if success:
        print("\n🎊 概览图修复最终测试成功！")
        print("✅ 所有visualize_overview调用都正常工作")
    else:
        print("\n⚠️ 概览图修复最终测试发现问题")
