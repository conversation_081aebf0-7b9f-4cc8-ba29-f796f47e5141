#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试批量概览颜色问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_batch_overview_color():
    """调试批量概览颜色获取问题"""
    
    print("🔍 调试批量概览颜色获取问题")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        # 创建修复实例
        fix = VisualizationPerformanceFix()
        
        # 模拟真实的调用场景
        print("📋 模拟真实调用场景...")
        
        # 模拟实体数据
        test_entities = []
        for i in range(5):
            entity = {
                'type': 'LINE',
                'layer': f'Layer_{i}',
                'label': 'wall' if i < 2 else ('door_window' if i < 4 else None),
                'points': [[i*10, 0], [(i+1)*10, 0]],
                'id': f'entity_{i}'
            }
            test_entities.append(entity)
        
        print(f"  创建了 {len(test_entities)} 个测试实体")
        
        # 模拟组数据
        all_groups = [
            [test_entities[0], test_entities[1]],  # 组1：墙体
            [test_entities[2], test_entities[3]],  # 组2：门窗
            [test_entities[4]]                     # 组3：未标注
        ]
        
        groups_info = [
            {'status': 'labeled', 'label': 'wall', 'is_current_group': False},
            {'status': 'labeled', 'label': 'door_window', 'is_current_group': True},
            {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}
        ]
        
        print(f"  创建了 {len(all_groups)} 个组，{len(groups_info)} 个组信息")
        
        # 模拟处理器
        class MockProcessor:
            def __init__(self):
                self.all_groups = all_groups
                self.groups_info = groups_info
        
        processor = MockProcessor()
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = {
                    'wall': '#8B4513',
                    'door_window': '#FFD700',
                    'furniture': '#4DB6AC',
                    'current_group': '#FF0000',
                    'unlabeled': '#C0C0C0',
                    'other': '#808080'
                }
        
        visualizer = MockVisualizer()
        
        # 模拟标注实体
        labeled_entities = [test_entities[0], test_entities[1], test_entities[2], test_entities[3]]
        
        print("\n🧪 测试批量概览颜色获取...")
        
        # 测试每个实体的颜色获取
        for i, entity in enumerate(test_entities):
            print(f"\n实体 {i+1}: {entity['layer']} | 标签: {entity.get('label', 'None')}")
            
            try:
                # 调用批量概览的颜色获取方法
                color = fix._get_entity_color_for_batch_enhanced(
                    entity, 
                    visualizer.color_scheme, 
                    visualizer, 
                    labeled_entities, 
                    all_groups, 
                    groups_info, 
                    processor
                )
                
                print(f"  获取到的颜色: {color}")
                
                # 验证颜色是否正确
                expected_colors = {
                    0: '#8B4513',  # 墙体
                    1: '#8B4513',  # 墙体
                    2: '#FF0000',  # 当前组（门窗）
                    3: '#FF0000',  # 当前组（门窗）
                    4: '#C0C0C0'   # 未标注
                }
                
                expected = expected_colors.get(i, '#808080')
                if color == expected:
                    print(f"  ✅ 颜色正确")
                else:
                    print(f"  ❌ 颜色错误，期望: {expected}")
                    
            except Exception as e:
                print(f"  ❌ 颜色获取失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n" + "=" * 50)
        print("🎯 调试结果分析:")
        
        # 检查组信息匹配
        print("\n📊 组信息匹配检查:")
        for i, entity in enumerate(test_entities):
            entity_id = id(entity)
            found_group = False
            
            for group_index, group in enumerate(all_groups):
                if group and any(id(e) == entity_id for e in group):
                    group_info = groups_info[group_index] if group_index < len(groups_info) else None
                    print(f"  实体{i+1} -> 组{group_index+1}: {group_info}")
                    found_group = True
                    break
            
            if not found_group:
                print(f"  实体{i+1} -> 未找到对应组")
        
        # 检查配色方案
        print(f"\n🎨 配色方案检查:")
        for key, value in visualizer.color_scheme.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_color_mapping():
    """测试简单的颜色映射"""
    
    print("\n🎨 测试简单颜色映射")
    print("-" * 30)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 测试组信息颜色获取
        test_cases = [
            {
                'group_info': {'status': 'labeled', 'label': 'wall', 'is_current_group': False},
                'expected': '#8B4513',
                'description': '已标注墙体组'
            },
            {
                'group_info': {'status': 'labeled', 'label': 'door_window', 'is_current_group': True},
                'expected': '#FF0000',
                'description': '当前组（门窗）'
            },
            {
                'group_info': {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False},
                'expected': '#C0C0C0',
                'description': '未标注组'
            }
        ]
        
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'current_group': '#FF0000',
            'unlabeled': '#C0C0C0',
            'other': '#808080'
        }
        
        for i, case in enumerate(test_cases):
            print(f"\n测试 {i+1}: {case['description']}")
            print(f"  组信息: {case['group_info']}")
            
            color = fix._get_color_from_group_info(case['group_info'], color_scheme)
            
            print(f"  获取颜色: {color}")
            print(f"  期望颜色: {case['expected']}")
            
            if color == case['expected']:
                print(f"  ✅ 测试通过")
            else:
                print(f"  ❌ 测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单颜色映射测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 批量概览颜色调试程序")
    print("=" * 50)
    
    # 运行调试
    debug_result = debug_batch_overview_color()
    
    # 运行简单测试
    simple_result = test_simple_color_mapping()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结:")
    
    if debug_result and simple_result:
        print("✅ 所有测试通过")
        print("✅ 批量概览颜色获取逻辑正常")
        print("📋 如果实际使用中仍显示灰色，可能的原因：")
        print("  1. 处理器参数未正确传递")
        print("  2. 组信息数据为空或格式不正确")
        print("  3. 配色方案未正确设置")
        print("  4. 实体与组的匹配逻辑有问题")
    else:
        print("❌ 部分测试失败")
        print("❌ 需要进一步检查批量概览颜色获取逻辑")
    
    print("\n💡 建议：")
    print("  1. 检查实际调用时的调试输出")
    print("  2. 确认处理器参数是否正确传递")
    print("  3. 验证组信息数据的完整性")
    print("  4. 检查配色方案的设置")
