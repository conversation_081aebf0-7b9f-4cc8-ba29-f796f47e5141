#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试传统颜色选择器功能
验证colorchooser是否能正常工作
"""

import os
import sys
import tkinter as tk
from tkinter import colorchooser, messagebox, Button, Frame, Label

def test_traditional_color_picker():
    """测试传统颜色选择器"""
    print("🔍 测试传统颜色选择器功能")
    print("=" * 80)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("传统颜色选择器测试")
        root.geometry("400x300")
        
        print("✅ 创建测试窗口成功")
        
        # 创建主框架
        main_frame = Frame(root, padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # 标题
        Label(main_frame, text="传统颜色选择器测试", 
              font=('Arial', 16, 'bold')).pack(pady=(0, 20))
        
        # 当前颜色显示
        current_color = "#8B4513"
        color_var = tk.StringVar(value=current_color)
        
        # 颜色显示框架
        color_display_frame = Frame(main_frame)
        color_display_frame.pack(fill='x', pady=(0, 20))
        
        Label(color_display_frame, text="当前颜色:", font=('Arial', 12)).pack(side='left')
        
        # 颜色预览框
        color_preview = Frame(color_display_frame, width=50, height=30, 
                            bg=current_color, relief='solid', borderwidth=2)
        color_preview.pack(side='left', padx=(10, 10))
        color_preview.pack_propagate(False)
        
        # 颜色值标签
        color_label = Label(color_display_frame, text=current_color, 
                          font=('Arial', 12, 'bold'))
        color_label.pack(side='left')
        
        def test_colorchooser():
            """测试colorchooser功能"""
            try:
                print("📋 打开传统颜色选择器...")
                
                # 调用传统颜色选择器
                result = colorchooser.askcolor(
                    color=color_var.get(),
                    title="选择颜色"
                )
                
                print(f"✅ 颜色选择器返回结果: {result}")
                
                if result[1]:  # 用户选择了颜色
                    new_color = result[1].upper()
                    color_var.set(new_color)
                    
                    # 更新颜色预览
                    color_preview.config(bg=new_color)
                    color_label.config(text=new_color)
                    
                    print(f"✅ 颜色已更新为: {new_color}")
                    messagebox.showinfo("成功", f"颜色已更新为: {new_color}")
                else:
                    print("ℹ️ 用户取消了颜色选择")
                    
            except Exception as e:
                print(f"❌ 颜色选择器测试失败: {e}")
                import traceback
                traceback.print_exc()
                messagebox.showerror("错误", f"颜色选择器测试失败: {e}")
        
        def test_direct_import():
            """测试直接导入colorchooser"""
            try:
                print("📋 测试直接导入colorchooser...")
                
                # 测试导入
                import tkinter.colorchooser as cc
                print("✅ tkinter.colorchooser 导入成功")
                
                # 测试方法存在性
                if hasattr(cc, 'askcolor'):
                    print("✅ askcolor 方法存在")
                else:
                    print("❌ askcolor 方法不存在")
                
                # 测试调用
                result = cc.askcolor(title="测试导入")
                print(f"✅ 直接导入测试成功: {result}")
                
                if result[1]:
                    messagebox.showinfo("成功", f"直接导入测试成功: {result[1]}")
                
            except Exception as e:
                print(f"❌ 直接导入测试失败: {e}")
                messagebox.showerror("错误", f"直接导入测试失败: {e}")
        
        # 按钮框架
        button_frame = Frame(main_frame)
        button_frame.pack(fill='x', pady=(0, 20))
        
        # 测试按钮
        Button(button_frame, text="🎨 测试传统颜色选择器", 
               command=test_colorchooser,
               bg='#2196F3', fg='white', font=('Arial', 12), 
               width=20).pack(pady=5)
        
        Button(button_frame, text="🔧 测试直接导入", 
               command=test_direct_import,
               bg='#FF9800', fg='white', font=('Arial', 12), 
               width=20).pack(pady=5)
        
        # 说明文字
        info_frame = Frame(main_frame)
        info_frame.pack(fill='x')
        
        info_text = """
测试说明:
1. 点击"测试传统颜色选择器"按钮
2. 应该弹出系统的颜色选择对话框
3. 选择颜色后应该更新显示
4. 如果失败，尝试"测试直接导入"

如果两个测试都失败，可能是系统环境问题。
        """
        
        Label(info_frame, text=info_text, font=('Arial', 10), 
              justify='left', fg='#666666').pack(anchor='w')
        
        print("\n🔧 测试环境信息:")
        print(f"   Python版本: {sys.version}")
        print(f"   Tkinter版本: {tk.TkVersion}")
        
        # 检查colorchooser是否可用
        try:
            import tkinter.colorchooser
            print("✅ tkinter.colorchooser 模块可用")
        except ImportError as e:
            print(f"❌ tkinter.colorchooser 模块不可用: {e}")
        
        print(f"\n💡 请点击按钮测试传统颜色选择器功能")
        print("=" * 80)
        
        # 启动主循环
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        try:
            root.destroy()
        except:
            pass
        return False

if __name__ == "__main__":
    print("🚀 开始测试传统颜色选择器功能")
    
    success = test_traditional_color_picker()
    
    if success:
        print("\n✅ 测试完成")
        print("如果颜色选择器正常工作，说明功能实现正确")
    else:
        print("\n❌ 测试失败")
