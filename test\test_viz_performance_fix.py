#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可视化性能修复效果
验证新的可视化性能修复是否解决了等待时间过长的问题
"""

import os
import sys
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from visualization_performance_fix import apply_visualization_performance_fix, VisualizationPerformanceFix
    print("✅ 可视化性能修复模块导入成功")
except ImportError as e:
    print(f"❌ 可视化性能修复模块导入失败: {e}")
    sys.exit(1)

# 尝试导入matplotlib
try:
    import matplotlib.pyplot as plt
    MATPLOTLIB_AVAILABLE = True
    print("✅ Matplotlib导入成功")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️ Matplotlib不可用，将使用模拟测试")


class TestVisualizer:
    """测试可视化器"""
    
    def __init__(self):
        """初始化测试可视化器"""
        self.draw_calls = 0
        self.overview_calls = 0
        
        if MATPLOTLIB_AVAILABLE:
            self.fig, (self.ax_detail, self.ax_overview) = plt.subplots(1, 2, figsize=(12, 6))
        else:
            self.ax_detail = MockAxis()
            self.ax_overview = MockAxis()
    
    def draw_entities(self, entities):
        """原始绘制实体方法（模拟慢速绘制）"""
        self.draw_calls += 1
        
        if not entities:
            return
        
        print(f"🎨 原始绘制: {len(entities)} 个实体")
        
        # 模拟原始方法的慢速绘制
        if MATPLOTLIB_AVAILABLE:
            self.ax_detail.clear()
            self.ax_detail.set_aspect('equal')
            self.ax_detail.grid(True, linestyle='--', alpha=0.3)
            
            # 逐个绘制实体（慢）
            for i, entity in enumerate(entities):
                if entity.get('type') == 'LINE' and 'points' in entity:
                    points = entity['points']
                    if len(points) >= 2:
                        x = [p[0] for p in points[:2]]
                        y = [p[1] for p in points[:2]]
                        color = 'blue' if entity.get('layer') == 'A-WALL' else 'red'
                        self.ax_detail.plot(x, y, color=color, linewidth=1.0, alpha=0.8)
                
                # 模拟每个实体的绘制时间
                if i % 20 == 0:
                    time.sleep(0.001)
        else:
            # 模拟绘制时间
            time.sleep(0.001 * len(entities))
    
    def visualize_overview(self, all_entities, current_group_entities=None, 
                         labeled_entities=None, **kwargs):
        """原始概览可视化方法（模拟慢速概览）"""
        self.overview_calls += 1
        
        print(f"🌍 原始概览: {len(all_entities)} 个实体")
        
        # 模拟原始方法的慢速概览
        if MATPLOTLIB_AVAILABLE:
            self.ax_overview.clear()
            self.ax_overview.set_aspect('equal')
            self.ax_overview.grid(True, linestyle='--', alpha=0.3)
            
            # 模拟组查找和绘制（慢）
            for i, entity in enumerate(all_entities):
                # 模拟组查找的开销
                time.sleep(0.00005)  # 每个实体0.05毫秒的查找时间
                
                if entity.get('type') == 'LINE' and 'points' in entity:
                    points = entity['points']
                    if len(points) >= 2:
                        x = [p[0] for p in points[:2]]
                        y = [p[1] for p in points[:2]]
                        
                        # 简单的颜色选择
                        if current_group_entities and entity in current_group_entities:
                            color = 'red'
                        elif labeled_entities and entity in labeled_entities:
                            color = 'green'
                        else:
                            color = 'lightgray'
                        
                        self.ax_overview.plot(x, y, color=color, linewidth=0.5, alpha=0.7)
                
                # 模拟批处理
                if i % 50 == 0:
                    time.sleep(0.001)
        else:
            # 模拟概览时间（包括组查找开销）
            time.sleep(0.00005 * len(all_entities))  # 组查找时间
            time.sleep(0.001 * len(all_entities))    # 绘制时间


class MockAxis:
    """模拟matplotlib轴对象"""
    
    def clear(self):
        pass
    
    def set_aspect(self, aspect):
        pass
    
    def grid(self, *args, **kwargs):
        pass
    
    def plot(self, x, y, *args, **kwargs):
        pass
    
    def set_title(self, title, **kwargs):
        pass
    
    def legend(self, **kwargs):
        pass


def create_test_entities(count=484):
    """创建测试实体"""
    entities = []
    
    # A-WALL图层
    wall_count = int(count * 0.43)  # 约43%
    for i in range(wall_count):
        entities.append({
            'id': f'wall_{i}',
            'type': 'LINE',
            'layer': 'A-WALL',
            'start_point': [i * 5.0, 0.0],
            'end_point': [i * 5.0 + 3.0, 0.0],
            'color': 1,
            'points': [[i * 5.0, 0.0], [i * 5.0 + 3.0, 0.0]]
        })
    
    # A-WINDOW图层
    window_count = int(count * 0.32)  # 约32%
    for i in range(window_count):
        entities.append({
            'id': f'window_{i}',
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'start_point': [i * 8.0, 10.0],
            'end_point': [i * 8.0 + 4.0, 10.0],
            'color': 2,
            'points': [[i * 8.0, 10.0], [i * 8.0 + 4.0, 10.0]]
        })
    
    # 0图层
    remaining_count = count - wall_count - window_count
    for i in range(remaining_count):
        entities.append({
            'id': f'layer0_{i}',
            'type': 'LINE',
            'layer': '0',
            'start_point': [i * 6.0, 20.0],
            'end_point': [i * 6.0 + 2.0, 20.0],
            'color': 3,
            'points': [[i * 6.0, 20.0], [i * 6.0 + 2.0, 20.0]]
        })
    
    return entities


def test_visualization_performance_fix():
    """测试可视化性能修复"""
    print("🧪 测试可视化性能修复")
    print("=" * 80)
    
    # 创建测试数据
    test_entities = create_test_entities(484)
    current_group = test_entities[:50]
    labeled_entities = test_entities[50:100]
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    print(f"   当前组: {len(current_group)} 个实体")
    print(f"   已标注: {len(labeled_entities)} 个实体")
    
    # 测试原始性能
    print(f"\n📊 测试原始可视化性能")
    original_visualizer = TestVisualizer()
    
    # 原始绘制测试
    draw_start = time.time()
    original_visualizer.draw_entities(test_entities)
    original_draw_time = time.time() - draw_start
    
    # 原始概览测试
    overview_start = time.time()
    original_visualizer.visualize_overview(test_entities, current_group, labeled_entities)
    original_overview_time = time.time() - overview_start
    
    original_total = original_draw_time + original_overview_time
    
    print(f"   原始绘制时间: {original_draw_time:.3f} 秒")
    print(f"   原始概览时间: {original_overview_time:.3f} 秒")
    print(f"   原始总时间: {original_total:.3f} 秒")
    
    # 测试修复后性能
    print(f"\n📊 测试修复后可视化性能")
    fixed_visualizer = TestVisualizer()
    
    # 应用性能修复
    apply_visualization_performance_fix(fixed_visualizer)
    
    # 修复后绘制测试
    draw_start = time.time()
    fixed_visualizer.draw_entities(test_entities)
    fixed_draw_time = time.time() - draw_start
    
    # 修复后概览测试
    overview_start = time.time()
    fixed_visualizer.visualize_overview(test_entities, current_group, labeled_entities)
    fixed_overview_time = time.time() - overview_start
    
    fixed_total = fixed_draw_time + fixed_overview_time
    
    print(f"   修复后绘制时间: {fixed_draw_time:.3f} 秒")
    print(f"   修复后概览时间: {fixed_overview_time:.3f} 秒")
    print(f"   修复后总时间: {fixed_total:.3f} 秒")
    
    # 计算性能提升
    draw_speedup = original_draw_time / fixed_draw_time if fixed_draw_time > 0 else float('inf')
    overview_speedup = original_overview_time / fixed_overview_time if fixed_overview_time > 0 else float('inf')
    total_speedup = original_total / fixed_total if fixed_total > 0 else float('inf')
    
    print(f"\n🚀 性能提升分析:")
    print(f"   绘制性能提升: {draw_speedup:.1f}x")
    print(f"   概览性能提升: {overview_speedup:.1f}x")
    print(f"   总体性能提升: {total_speedup:.1f}x")
    
    # 时间节省
    time_saved = original_total - fixed_total
    print(f"   节省时间: {time_saved:.3f} 秒")
    
    return {
        'original_total': original_total,
        'fixed_total': fixed_total,
        'total_speedup': total_speedup,
        'time_saved': time_saved
    }


def run_visualization_fix_test():
    """运行可视化修复测试"""
    print("🚀 开始可视化性能修复测试")
    print("=" * 100)
    
    overall_start = time.time()
    
    try:
        # 测试基本性能修复
        basic_results = test_visualization_performance_fix()
        
        overall_time = time.time() - overall_start
        
        print(f"\n🎉 可视化性能修复测试完成")
        print("=" * 100)
        print(f"   总测试时间: {overall_time:.2f} 秒")
        
        # 综合分析
        print(f"\n📊 综合性能分析:")
        print(f"   性能提升: {basic_results['total_speedup']:.1f}x")
        print(f"   节省时间: {basic_results['time_saved']:.3f} 秒")
        
        # 评估修复效果
        print(f"\n🎯 修复策略:")
        print(f"   - 批量绘制：按图层分组，一次性matplotlib调用")
        print(f"   - 快速查找：使用ID集合进行O(1)查找")
        print(f"   - 冗余跳过：避免重复的可视化更新")

        if basic_results['total_speedup'] > 10 and basic_results['time_saved'] > 0.5:
            print(f"   🎉 可视化性能修复极其成功！")
            print(f"   ⚡ 应该能显著改善用户等待时间")
        elif basic_results['total_speedup'] > 5 and basic_results['time_saved'] > 0.3:
            print(f"   ✅ 可视化性能修复成功！")
        elif basic_results['total_speedup'] > 2:
            print(f"   ✅ 可视化性能有所改善")
        else:
            print(f"   ⚠️ 可视化性能修复效果有限")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_visualization_fix_test()
    
    if success:
        print(f"\n🎊 可视化性能修复测试成功！")
        print(f"   线条处理等待时间问题应该得到显著改善")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)
