#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试视图切换按钮功能
检查是否有重复的视图切换按钮，并验证功能是否正常
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_view_switching_buttons():
    """测试视图切换按钮功能"""
    print("🔍 开始测试视图切换按钮功能...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("视图切换按钮测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(1)
        
        # 测试1：检查视图切换按钮的存在性
        print("\n🔍 测试1：检查视图切换按钮的存在性")
        
        view_switch_buttons = []
        
        # 检查左下角图像控制区域的视图切换按钮
        if hasattr(app, 'view_switch_button'):
            button = app.view_switch_button
            if button:
                try:
                    text = button.cget('text')
                    bg = button.cget('bg')
                    state = button.cget('state')
                    parent = button.master
                    print(f"  ✅ 左下角视图切换按钮: 文本='{text}', 背景={bg}, 状态={state}")
                    print(f"    父容器: {parent}")
                    view_switch_buttons.append(('左下角', button))
                except Exception as e:
                    print(f"  ❌ 左下角视图切换按钮属性获取失败: {e}")
            else:
                print(f"  ❌ 左下角视图切换按钮对象为空")
        else:
            print(f"  ❌ 左下角视图切换按钮属性不存在")
        
        # 检查是否还有其他视图切换按钮（搜索所有可能的属性名）
        possible_button_names = [
            'view_switch_btn',
            'toggle_view_button',
            'switch_view_button',
            'alternative_view_button'
        ]
        
        for button_name in possible_button_names:
            if hasattr(app, button_name):
                button = getattr(app, button_name)
                if button:
                    try:
                        text = button.cget('text')
                        bg = button.cget('bg')
                        print(f"  ⚠️ 发现额外的视图切换按钮 ({button_name}): 文本='{text}', 背景={bg}")
                        view_switch_buttons.append((button_name, button))
                    except:
                        pass
        
        # 测试2：检查视图切换功能是否正常
        print("\n🔍 测试2：检查视图切换功能是否正常")
        
        if len(view_switch_buttons) == 0:
            print("  ❌ 没有找到任何视图切换按钮")
            return False
        elif len(view_switch_buttons) > 1:
            print(f"  ⚠️ 发现 {len(view_switch_buttons)} 个视图切换按钮，可能存在重复")
            for name, button in view_switch_buttons:
                print(f"    - {name}: {button}")
        else:
            print(f"  ✅ 找到唯一的视图切换按钮")
        
        # 使用第一个找到的按钮进行功能测试
        test_button_name, test_button = view_switch_buttons[0]
        print(f"  使用 {test_button_name} 进行功能测试...")
        
        # 检查视图切换相关的方法和属性
        required_methods = [
            '_toggle_view_mode',
            '_switch_to_original_view',
            '_switch_to_alternative_view'
        ]
        
        required_attributes = [
            'current_view_mode',
            'view_switching_enabled'
        ]
        
        print(f"\n  检查必需的方法:")
        for method_name in required_methods:
            if hasattr(app, method_name):
                print(f"    ✅ {method_name} - 存在")
            else:
                print(f"    ❌ {method_name} - 缺失")
        
        print(f"\n  检查必需的属性:")
        for attr_name in required_attributes:
            if hasattr(app, attr_name):
                value = getattr(app, attr_name)
                print(f"    ✅ {attr_name} = {value}")
            else:
                print(f"    ❌ {attr_name} - 缺失")
        
        # 测试3：实际测试视图切换功能
        print("\n🔍 测试3：实际测试视图切换功能")
        
        try:
            # 记录初始状态
            initial_text = test_button.cget('text')
            initial_mode = getattr(app, 'current_view_mode', 'unknown')
            print(f"  初始状态: 按钮文本='{initial_text}', 视图模式={initial_mode}")
            
            # 第一次切换
            print(f"  执行第一次视图切换...")
            if hasattr(app, '_toggle_view_mode'):
                app._toggle_view_mode()
                root.update()
                time.sleep(0.5)
                
                after_text_1 = test_button.cget('text')
                after_mode_1 = getattr(app, 'current_view_mode', 'unknown')
                print(f"  第一次切换后: 按钮文本='{after_text_1}', 视图模式={after_mode_1}")
                
                if initial_text != after_text_1:
                    print(f"    ✅ 按钮文本正确变化")
                else:
                    print(f"    ❌ 按钮文本未变化")
                
                if initial_mode != after_mode_1:
                    print(f"    ✅ 视图模式正确变化")
                else:
                    print(f"    ❌ 视图模式未变化")
                
                # 第二次切换（恢复）
                print(f"  执行第二次视图切换（恢复）...")
                app._toggle_view_mode()
                root.update()
                time.sleep(0.5)
                
                after_text_2 = test_button.cget('text')
                after_mode_2 = getattr(app, 'current_view_mode', 'unknown')
                print(f"  第二次切换后: 按钮文本='{after_text_2}', 视图模式={after_mode_2}")
                
                if after_text_2 == initial_text:
                    print(f"    ✅ 按钮文本正确恢复")
                else:
                    print(f"    ❌ 按钮文本未正确恢复")
                
                if after_mode_2 == initial_mode:
                    print(f"    ✅ 视图模式正确恢复")
                else:
                    print(f"    ❌ 视图模式未正确恢复")
                
            else:
                print(f"  ❌ _toggle_view_mode 方法不存在")
                return False
                
        except Exception as e:
            print(f"  ❌ 视图切换功能测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试4：检查是否有重复的右上角按钮
        print("\n🔍 测试4：检查是否有重复的右上角按钮")
        
        # 搜索可能的右上角按钮
        def find_widgets_by_text(parent, target_text):
            """递归搜索包含特定文本的按钮"""
            found_widgets = []
            
            def search_recursive(widget):
                try:
                    # 检查当前组件
                    if hasattr(widget, 'cget'):
                        try:
                            text = widget.cget('text')
                            if target_text in text:
                                found_widgets.append(widget)
                        except:
                            pass
                    
                    # 递归搜索子组件
                    if hasattr(widget, 'winfo_children'):
                        for child in widget.winfo_children():
                            search_recursive(child)
                except:
                    pass
            
            search_recursive(parent)
            return found_widgets
        
        # 搜索包含"视图"或"切换"的按钮
        view_related_buttons = []
        for text in ['视图', '切换', '替代']:
            buttons = find_widgets_by_text(root, text)
            for button in buttons:
                try:
                    button_text = button.cget('text')
                    button_bg = button.cget('bg')
                    view_related_buttons.append((button_text, button_bg, button))
                except:
                    pass
        
        print(f"  找到 {len(view_related_buttons)} 个包含视图相关文本的按钮:")
        for i, (text, bg, button) in enumerate(view_related_buttons):
            print(f"    {i+1}. 文本='{text}', 背景={bg}")
        
        if len(view_related_buttons) > 1:
            print(f"  ⚠️ 可能存在重复的视图切换按钮")
        else:
            print(f"  ✅ 没有发现重复的视图切换按钮")
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 视图切换按钮功能测试完成！")
        
        # 总结
        if len(view_switch_buttons) == 1 and hasattr(app, '_toggle_view_mode'):
            print("✅ 视图切换功能正常，没有重复按钮")
            return True
        else:
            print("⚠️ 视图切换功能可能存在问题或重复按钮")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_view_switching_buttons()
    if success:
        print("\n🎊 视图切换按钮测试成功！")
        print("✅ 功能正常工作")
        print("✅ 没有重复按钮")
    else:
        print("\n⚠️ 视图切换按钮测试发现问题")
        print("❌ 需要检查功能实现或清理重复按钮")
