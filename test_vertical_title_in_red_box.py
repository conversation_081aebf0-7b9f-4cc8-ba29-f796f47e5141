#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试竖向标题是否已正确移动到右侧红框区域（图层控制区域）
"""

import tkinter as tk
import sys
import os

def test_vertical_title_in_red_box():
    """测试竖向标题在红框区域的显示"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🔍 测试竖向标题在红框区域的显示...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("竖向标题红框区域测试")
        root.geometry("1000x700")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 检查_create_combined_image_control_area方法是否存在
        if hasattr(app, '_create_combined_image_control_area'):
            print("✅ _create_combined_image_control_area 方法存在")
            
            # 创建测试容器
            test_frame = tk.Frame(root, relief='ridge', bd=2, bg='lightgray')
            test_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 调用方法创建图像控制区域
            app._create_combined_image_control_area(test_frame)
            
            # 检查是否有竖向标题在图层控制区域
            vertical_title_found = False
            title_location = "未找到"
            
            def search_for_vertical_title(widget, path="root"):
                nonlocal vertical_title_found, title_location
                
                if isinstance(widget, tk.Label):
                    label_text = widget.cget('text')
                    if '图\n像\n控\n制' in label_text:
                        vertical_title_found = True
                        title_location = path
                        print(f"✅ 找到竖向标题: '{label_text}'")
                        print(f"  位置路径: {path}")
                        
                        # 检查标题的配置
                        font = widget.cget('font')
                        bg = widget.cget('bg')
                        fg = widget.cget('fg')
                        justify = widget.cget('justify')
                        print(f"  字体: {font}")
                        print(f"  背景色: {bg}")
                        print(f"  前景色: {fg}")
                        print(f"  对齐方式: {justify}")
                        return True
                
                # 递归搜索子组件
                try:
                    for child in widget.winfo_children():
                        if search_for_vertical_title(child, f"{path}->{type(child).__name__}"):
                            return True
                except:
                    pass
                
                return False
            
            # 搜索竖向标题
            search_for_vertical_title(test_frame)
            
            if not vertical_title_found:
                print("❌ 未找到竖向标题")
                return False
            
            print("✅ 竖向标题测试通过")
            
            # 显示窗口供用户查看
            print("📋 测试窗口已打开，请查看竖向标题在红框区域的效果")
            print("   关闭窗口以继续...")
            
            root.mainloop()
            return True
            
        else:
            print("❌ _create_combined_image_control_area 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_structure():
    """测试布局结构是否正确"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("\n🔍 测试布局结构...")
        
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = EnhancedCADAppV2(root)
        test_frame = tk.Frame(root)
        
        # 创建图像控制区域
        app._create_combined_image_control_area(test_frame)
        
        # 检查布局结构
        main_container_found = False
        layer_control_found = False
        zoom_buttons_found = False
        
        for child in test_frame.winfo_children():
            if isinstance(child, tk.Frame):
                main_container_found = True
                print(f"✅ 找到主容器")
                
                # 检查主容器的子组件
                for subchild in child.winfo_children():
                    if isinstance(subchild, tk.Frame):
                        if hasattr(app, 'layer_control_container') and subchild == app.layer_control_container:
                            layer_control_found = True
                            print(f"✅ 找到图层控制容器")
                        elif hasattr(app, 'zoom_buttons_container') and subchild == app.zoom_buttons_container:
                            zoom_buttons_found = True
                            print(f"✅ 找到缩放按钮容器")
        
        structure_correct = main_container_found and layer_control_found and zoom_buttons_found
        
        if structure_correct:
            print("✅ 布局结构正确")
        else:
            print(f"❌ 布局结构不完整:")
            print(f"  主容器: {'✅' if main_container_found else '❌'}")
            print(f"  图层控制: {'✅' if layer_control_found else '❌'}")
            print(f"  缩放按钮: {'✅' if zoom_buttons_found else '❌'}")
        
        root.destroy()
        return structure_correct
        
    except Exception as e:
        print(f"❌ 布局结构测试失败: {e}")
        return False

def create_layout_demo():
    """创建布局演示"""
    try:
        print("\n🎨 创建布局演示...")
        
        root = tk.Tk()
        root.title("图像控制区域布局演示")
        root.geometry("800x600")
        
        # 创建主容器
        main_frame = tk.Frame(root, bg='white')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 模拟当前布局结构
        # 主容器（垂直布局）
        main_container = tk.Frame(main_frame, bg='lightgray', relief='ridge', bd=2)
        main_container.pack(fill='both', expand=True)
        
        # 上部分：图层控制区域（带竖向标题）
        layer_control_frame = tk.Frame(main_container, bg='lightblue', relief='ridge', bd=1)
        layer_control_frame.pack(fill='both', expand=True, pady=(0, 2))
        
        # 图层控制区域的水平布局
        layer_horizontal = tk.Frame(layer_control_frame, bg='lightblue')
        layer_horizontal.pack(fill='both', expand=True)
        
        # 左侧：竖向标题
        title_frame = tk.Frame(layer_horizontal, width=25, bg='#F08080')
        title_frame.pack(side='left', fill='y', padx=(2, 0))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="图\n像\n控\n制",
                              font=('Arial', 9, 'bold'), bg='#F08080',
                              justify='center', fg='white')
        title_label.pack(expand=True, fill='both')
        
        # 右侧：图层列表内容
        list_frame = tk.Frame(layer_horizontal, bg='white')
        list_frame.pack(side='right', fill='both', expand=True, padx=(2, 0))
        
        content_label = tk.Label(list_frame, text="这里是图层控制列表\n\n包含CAD线条、墙体填充、\n家具填充、房间填充等",
                               font=('Arial', 10), bg='white')
        content_label.pack(expand=True)
        
        # 下部分：缩放按钮区域
        zoom_frame = tk.Frame(main_container, bg='lightyellow', relief='ridge', bd=1, height=100)
        zoom_frame.pack(fill='x', pady=(2, 0))
        zoom_frame.pack_propagate(False)
        
        zoom_label = tk.Label(zoom_frame, text="缩放按钮区域\n\n缩放查看 | 适应窗口 | 重置视图",
                             font=('Arial', 10), bg='lightyellow')
        zoom_label.pack(expand=True)
        
        print("✅ 演示窗口已创建")
        print("📋 请查看竖向标题在红框区域左侧的显示效果")
        print("   关闭窗口以继续...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ 演示创建失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("竖向标题红框区域测试")
    print("=" * 60)
    
    # 测试竖向标题在红框区域
    test1_result = test_vertical_title_in_red_box()
    
    # 测试布局结构
    test2_result = test_layout_structure()
    
    # 创建演示
    demo_result = create_layout_demo()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"竖向标题红框测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"布局结构测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"布局演示: {'✅ 通过' if demo_result else '❌ 失败'}")
    
    if test1_result and test2_result and demo_result:
        print("🎉 所有测试通过！竖向标题已成功移动到红框区域左侧")
    else:
        print("⚠️ 部分测试失败，请检查实现")
    
    print("=" * 60)
