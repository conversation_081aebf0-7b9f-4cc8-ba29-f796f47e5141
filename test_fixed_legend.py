#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的索引图功能
验证：
1. 删除了右上角多余的索引图
2. 下方可折叠索引图能正常显示内容
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fixed_legend():
    """测试修复后的索引图功能"""
    print("🔍 开始测试修复后的索引图...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("修复后索引图测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(3)
        
        # 测试1：检查是否只有一个索引图
        print("\n🔍 测试1：检查索引图数量")
        
        legend_components = []
        
        # 检查可折叠索引图组件
        if hasattr(app, 'legend_container'):
            legend_components.append(('可折叠索引图容器', app.legend_container))
            
        if hasattr(app, 'legend_canvas'):
            legend_components.append(('索引图画布', app.legend_canvas))
            
        if hasattr(app, 'legend_ax'):
            legend_components.append(('索引图轴', app.legend_ax))
        
        print(f"    找到索引图组件数量: {len(legend_components)}")
        for name, component in legend_components:
            print(f"    ✅ {name}: {type(component)}")
        
        # 测试2：检查可折叠索引图是否在底部
        print("\n🔍 测试2：检查可折叠索引图位置")
        
        if hasattr(app, 'legend_container'):
            container = app.legend_container
            try:
                # 获取容器信息
                container_info = container.pack_info()
                print(f"    容器布局信息: {container_info}")
                
                # 检查是否在底部
                if container_info.get('side') == 'bottom':
                    print("    ✅ 索引图正确位置在底部")
                else:
                    print(f"    ⚠️ 索引图位置: {container_info.get('side', '未知')}")
                    
            except Exception as e:
                print(f"    ❌ 获取容器信息失败: {e}")
        
        # 测试3：测试索引图内容显示
        print("\n🔍 测试3：测试索引图内容显示")
        
        # 展开索引图
        if hasattr(app, 'legend_collapsed') and app.legend_collapsed.get():
            print("    展开索引图...")
            app._toggle_legend()
            root.update()
            time.sleep(2)
        
        # 检查索引图是否有内容
        if hasattr(app, 'legend_ax'):
            try:
                # 获取轴上的所有对象
                children = app.legend_ax.get_children()
                print(f"    索引图轴上的对象数量: {len(children)}")
                
                # 检查是否有文本或图形对象
                text_objects = [child for child in children if hasattr(child, 'get_text')]
                patch_objects = [child for child in children if hasattr(child, 'get_facecolor')]
                
                print(f"    文本对象数量: {len(text_objects)}")
                print(f"    图形对象数量: {len(patch_objects)}")
                
                if text_objects or patch_objects:
                    print("    ✅ 索引图有内容显示")
                    
                    # 显示一些文本内容
                    for i, text_obj in enumerate(text_objects[:3]):
                        try:
                            text_content = text_obj.get_text()
                            if text_content.strip():
                                print(f"      文本{i+1}: '{text_content}'")
                        except:
                            pass
                else:
                    print("    ⚠️ 索引图暂无内容")
                    
            except Exception as e:
                print(f"    ❌ 检查索引图内容失败: {e}")
        
        # 测试4：测试基本配色索引功能
        print("\n🔍 测试4：测试基本配色索引功能")
        
        if hasattr(app, '_draw_basic_color_legend'):
            try:
                print("    调用基本配色索引绘制...")
                app._draw_basic_color_legend()
                
                if hasattr(app, 'legend_canvas'):
                    app.legend_canvas.draw()
                    
                root.update()
                time.sleep(1)
                
                print("    ✅ 基本配色索引绘制成功")
                
            except Exception as e:
                print(f"    ❌ 基本配色索引绘制失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("    ❌ _draw_basic_color_legend 方法不存在")
        
        # 测试5：测试折叠/展开功能
        print("\n🔍 测试5：测试折叠/展开功能")
        
        if hasattr(app, '_toggle_legend'):
            try:
                # 测试折叠
                print("    测试折叠功能...")
                app._toggle_legend()
                root.update()
                time.sleep(1)
                
                collapsed_state = app.legend_collapsed.get()
                btn_text = app.legend_toggle_btn.cget('text')
                print(f"    折叠后状态: {'折叠' if collapsed_state else '展开'}")
                print(f"    按钮文本: '{btn_text}'")
                
                # 测试展开
                print("    测试展开功能...")
                app._toggle_legend()
                root.update()
                time.sleep(1)
                
                expanded_state = app.legend_collapsed.get()
                btn_text = app.legend_toggle_btn.cget('text')
                print(f"    展开后状态: {'折叠' if expanded_state else '展开'}")
                print(f"    按钮文本: '{btn_text}'")
                
                print("    ✅ 折叠/展开功能正常")
                
            except Exception as e:
                print(f"    ❌ 折叠/展开功能测试失败: {e}")
        
        # 测试6：检查配色方案是否加载
        print("\n🔍 测试6：检查配色方案")
        
        if hasattr(app, 'current_color_scheme'):
            color_scheme = app.current_color_scheme
            if color_scheme:
                print(f"    配色方案已加载，包含 {len(color_scheme)} 种颜色")
                
                # 显示一些主要颜色
                main_colors = ['wall', 'door', 'window', 'furniture', 'room']
                for color_key in main_colors:
                    if color_key in color_scheme:
                        color_value = color_scheme[color_key]
                        print(f"      {color_key}: {color_value}")
                        
                print("    ✅ 配色方案正常")
            else:
                print("    ⚠️ 配色方案未加载")
        else:
            print("    ❌ 配色方案属性不存在")
        
        # 等待用户观察
        print("\n⏳ 等待5秒让用户观察修复后的索引图...")
        time.sleep(5)
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 修复后索引图测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixed_legend()
    if success:
        print("\n🎊 修复后索引图测试成功！")
        print("\n📋 测试总结:")
        print("   ✅ 删除了右上角多余的索引图")
        print("   ✅ 下方可折叠索引图能正常显示")
        print("   ✅ 基本配色索引功能正常")
        print("   ✅ 折叠/展开功能正常")
    else:
        print("\n⚠️ 修复后索引图测试发现问题")
