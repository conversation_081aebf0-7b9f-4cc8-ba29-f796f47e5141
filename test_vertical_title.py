#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试竖向标题显示效果
"""

import tkinter as tk
import sys
import os

def test_vertical_title():
    """测试竖向标题显示"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🔍 测试竖向标题显示...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("竖向标题测试")
        root.geometry("800x600")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 检查_create_combined_image_control_area方法是否存在
        if hasattr(app, '_create_combined_image_control_area'):
            print("✅ _create_combined_image_control_area 方法存在")
            
            # 创建测试容器
            test_frame = tk.Frame(root, relief='ridge', bd=2, bg='lightgray')
            test_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 调用方法创建图像控制区域
            app._create_combined_image_control_area(test_frame)
            
            # 检查是否有竖向标题
            vertical_title_found = False
            for child in test_frame.winfo_children():
                if isinstance(child, tk.Frame):
                    for subchild in child.winfo_children():
                        if isinstance(subchild, tk.Frame):
                            for label in subchild.winfo_children():
                                if isinstance(label, tk.Label):
                                    label_text = label.cget('text')
                                    if '图\n像\n控\n制' in label_text:
                                        vertical_title_found = True
                                        print(f"✅ 找到竖向标题: '{label_text}'")
                                        
                                        # 检查标题的配置
                                        font = label.cget('font')
                                        bg = label.cget('bg')
                                        justify = label.cget('justify')
                                        print(f"  字体: {font}")
                                        print(f"  背景色: {bg}")
                                        print(f"  对齐方式: {justify}")
                                        break
            
            if not vertical_title_found:
                print("❌ 未找到竖向标题")
                return False
            
            print("✅ 竖向标题测试通过")
            
            # 显示窗口供用户查看
            print("📋 测试窗口已打开，请查看竖向标题效果")
            print("   关闭窗口以继续...")
            
            root.mainloop()
            return True
            
        else:
            print("❌ _create_combined_image_control_area 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_demo():
    """创建简单的竖向标题演示"""
    try:
        print("\n🎨 创建简单的竖向标题演示...")
        
        root = tk.Tk()
        root.title("竖向标题演示")
        root.geometry("400x300")
        
        # 创建主容器
        main_frame = tk.Frame(root, bg='white')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建水平布局容器
        horizontal_container = tk.Frame(main_frame, bg='white')
        horizontal_container.pack(fill='both', expand=True)
        
        # 左侧：竖向标题
        title_frame = tk.Frame(horizontal_container, width=25, bg='#F08080')
        title_frame.pack(side='left', fill='y', padx=(0, 5))
        title_frame.pack_propagate(False)
        
        # 竖向标题标签
        title_label = tk.Label(title_frame, text="图\n像\n控\n制",
                              font=('Arial', 9, 'bold'), bg='#F08080',
                              justify='center', fg='white')
        title_label.pack(expand=True, fill='both')
        
        # 右侧：内容区域
        content_frame = tk.Frame(horizontal_container, bg='lightblue')
        content_frame.pack(side='right', fill='both', expand=True)
        
        content_label = tk.Label(content_frame, text="这里是图像控制的主要内容区域\n\n包含图层控制和缩放按钮等功能",
                               font=('Arial', 10), bg='lightblue')
        content_label.pack(expand=True)
        
        print("✅ 演示窗口已创建")
        print("📋 请查看竖向标题的显示效果")
        print("   关闭窗口以继续...")
        
        root.mainloop()
        return True
        
    except Exception as e:
        print(f"❌ 演示创建失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("竖向标题测试")
    print("=" * 50)
    
    # 测试竖向标题
    test_result = test_vertical_title()
    
    print("\n" + "=" * 50)
    print("简单演示")
    print("=" * 50)
    
    # 创建简单演示
    demo_result = create_simple_demo()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"竖向标题测试: {'✅ 通过' if test_result else '❌ 失败'}")
    print(f"简单演示: {'✅ 通过' if demo_result else '❌ 失败'}")
    
    if test_result and demo_result:
        print("🎉 竖向标题功能已成功实现！")
    else:
        print("⚠️ 部分测试失败，请检查实现")
    
    print("=" * 50)
