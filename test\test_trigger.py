#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试触发脚本 - 直接调用修复方法进行测试
"""

import tkinter as tk

def test_trigger():
    """直接触发修复方法"""
    print("🚀 直接触发修复方法测试")
    print("=" * 80)
    
    try:
        # 创建应用实例
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        root = tk.Tk()
        root.withdraw()
        app = EnhancedCADAppV2(root)
        
        # 设置测试数据
        print("📋 设置测试数据...")
        test_groups = [
            [{'id': 1, 'type': 'LINE'}],
            [{'id': 2, 'type': 'LINE'}],
            [{'id': 3, 'type': 'LINE'}]
        ]
        
        groups_info = [
            {'status': 'labeled', 'entity_count': 1},
            {'status': 'labeled', 'entity_count': 1},
            {'status': 'unlabeled', 'entity_count': 1}
        ]
        
        app.processor.all_groups = test_groups
        app.processor.groups_info = groups_info
        app.processor.current_group_index = 2  # 最后一个组
        
        print(f"设置完成：{len(test_groups)}个组，当前组索引={app.processor.current_group_index}")
        
        # 直接调用修复方法
        print("\n🔄 直接调用修复方法...")
        
        print("1. 调用 _update_processor_completion_state...")
        app._update_processor_completion_state()
        
        print("2. 调用 _update_completion_group_list...")
        app._update_completion_group_list()
        
        print("3. 调用 _update_final_group_list...")
        app._update_final_group_list()
        
        print("4. 调用 update_group_list...")
        app.update_group_list()
        
        print("\n✅ 所有方法调用完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_trigger()
