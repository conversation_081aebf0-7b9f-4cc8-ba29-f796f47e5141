#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试缺失组问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_missing_groups():
    """调试为什么只有3个组被处理"""
    
    print("🔍 调试缺失组问题")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 模拟真实的34个组数据
        print("📊 模拟34个组的数据结构:")
        
        # 创建34个组，模拟您的真实数据
        all_groups = []
        groups_info = []
        all_entities = []
        
        # 组0-8：墙体组（auto_labeled + wall）
        for i in range(9):
            group_entities = []
            for j in range(10 + i):  # 不同大小的组
                entity = {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'points': [[i*10+j, 0], [i*10+j+5, 0]],
                    'id': f'wall_{i}_{j}'
                }
                group_entities.append(entity)
                all_entities.append(entity)
            
            all_groups.append(group_entities)
            groups_info.append({'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False})
        
        # 组9-24：门窗组（auto_labeled + door_window）
        for i in range(9, 25):
            group_entities = []
            for j in range(5):  # 门窗组较小
                entity = {
                    'type': 'LINE',
                    'layer': 'A-WINDOW',
                    'points': [[i*10+j, 10], [i*10+j+3, 10]],
                    'id': f'window_{i}_{j}'
                }
                group_entities.append(entity)
                all_entities.append(entity)
            
            all_groups.append(group_entities)
            groups_info.append({'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False})
        
        # 组25：标注中组（labeling）
        group_entities = []
        for j in range(8):
            entity = {
                'type': 'LINE',
                'layer': '',  # 空layer，模拟标注中的实体
                'points': [[250+j, 20], [250+j+2, 20]],
                'id': f'labeling_{j}'
            }
            group_entities.append(entity)
            all_entities.append(entity)
        
        all_groups.append(group_entities)
        groups_info.append({'status': 'labeling', 'label': '未标注', 'is_current_group': False})
        
        # 组26-33：未标注组（unlabeled）
        for i in range(26, 34):
            group_entities = []
            for j in range(3):  # 未标注组很小
                entity = {
                    'type': 'LINE',
                    'layer': 'A-OTHER',
                    'points': [[i*10+j, 30], [i*10+j+1, 30]],
                    'id': f'unlabeled_{i}_{j}'
                }
                group_entities.append(entity)
                all_entities.append(entity)
            
            all_groups.append(group_entities)
            groups_info.append({'status': 'unlabeled', 'label': '未标注', 'is_current_group': False})
        
        print(f"✅ 创建了{len(all_groups)}个组，总计{len(all_entities)}个实体")
        
        # 模拟配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#87CEEB',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
        
        visualizer = MockVisualizer()
        
        print("\n🧪 测试实体匹配情况:")
        print("=" * 40)
        
        # 统计每个组的匹配情况
        matched_groups = set()
        unmatched_groups = set()
        
        for entity in all_entities:
            color = fix._get_entity_color_for_batch_enhanced(
                entity, color_scheme, visualizer, [], all_groups, groups_info, None
            )
            
            # 从输出中提取组ID（如果有的话）
            # 这里我们需要手动检查哪个组被匹配了
            entity_id = id(entity)
            found_group = None
            
            for group_index, group in enumerate(all_groups):
                if any(id(e) == entity_id for e in group):
                    found_group = group_index
                    matched_groups.add(group_index)
                    break
            
            if found_group is None:
                print(f"  ❌ 实体未匹配到任何组: {entity.get('layer', 'unknown')}")
        
        # 找出未匹配的组
        for i in range(len(all_groups)):
            if i not in matched_groups:
                unmatched_groups.add(i)
        
        print(f"\n📊 匹配统计:")
        print(f"  匹配的组: {sorted(matched_groups)} (共{len(matched_groups)}个)")
        print(f"  未匹配的组: {sorted(unmatched_groups)} (共{len(unmatched_groups)}个)")
        
        if unmatched_groups:
            print(f"\n🔍 分析未匹配的组:")
            for group_id in sorted(list(unmatched_groups)[:5]):  # 只显示前5个
                group = all_groups[group_id]
                info = groups_info[group_id]
                print(f"  组{group_id}: {len(group)}个实体, 状态={info['status']}, 标签={info['label']}")
                if group:
                    sample_entity = group[0]
                    print(f"    示例实体: layer='{sample_entity.get('layer', '')}', type='{sample_entity.get('type', '')}'")
        
        return len(matched_groups), len(unmatched_groups)
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

def analyze_matching_logic():
    """分析匹配逻辑"""
    
    print("\n🔧 分析实体匹配逻辑")
    print("=" * 30)
    
    print("🎯 可能的问题原因:")
    print("  1. **ID匹配失败** - 实体对象在处理过程中被重新创建")
    print("  2. **属性匹配失败** - layer或type信息不匹配")
    print("  3. **组数据不完整** - 某些组为空或数据损坏")
    print("  4. **实体重复** - 同一个实体出现在多个组中")
    print("  5. **内存地址变化** - 实体对象的内存地址发生了变化")
    
    print("\n🔍 调试建议:")
    print("  1. 检查all_groups中每个组的实体数量")
    print("  2. 验证groups_info与all_groups的对应关系")
    print("  3. 测试ID匹配和属性匹配的成功率")
    print("  4. 确认实体对象的一致性")
    print("  5. 检查是否有组被意外过滤掉")

def provide_solution():
    """提供解决方案"""
    
    print("\n💡 解决方案建议")
    print("=" * 25)
    
    print("🔧 立即修复方案:")
    print("  1. **增强匹配逻辑** - 同时使用ID和属性匹配")
    print("  2. **添加详细日志** - 输出每个组的匹配情况")
    print("  3. **验证组数据** - 确保所有34个组都有有效数据")
    print("  4. **防御性编程** - 处理空组和异常情况")
    
    print("\n📝 具体实现:")
    print("  - 在_get_entity_color_for_batch_enhanced中添加组匹配统计")
    print("  - 输出每个组的匹配成功/失败情况")
    print("  - 检查实体对象的一致性")
    print("  - 确保所有组都被正确处理")

if __name__ == "__main__":
    print("🔍 缺失组问题调试程序")
    print("=" * 50)
    
    # 调试缺失组
    matched, unmatched = debug_missing_groups()
    
    # 分析匹配逻辑
    analyze_matching_logic()
    
    # 提供解决方案
    provide_solution()
    
    print("\n" + "=" * 50)
    print("🎉 调试总结:")
    
    if matched > 0:
        print(f"✅ 成功匹配 {matched} 个组")
        if unmatched > 0:
            print(f"⚠️ 有 {unmatched} 个组未匹配")
            print("💡 需要进一步调试匹配逻辑")
        else:
            print("✅ 所有组都成功匹配")
    else:
        print("❌ 没有组被匹配")
        print("💡 需要检查基础的匹配逻辑")
    
    print("\n🚀 下一步:")
    print("  1. 在实际程序中添加组匹配统计")
    print("  2. 输出每个组的处理情况")
    print("  3. 确认所有34个组都被正确处理")
