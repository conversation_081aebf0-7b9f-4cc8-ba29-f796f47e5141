#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试概览图数据流
检查概览图绘制时的数据传递情况
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_overview_data_flow():
    """测试概览图数据流"""
    print("🔍 开始测试概览图数据流...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("概览图数据流测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(1)
        
        # 测试1：检查处理器的组信息
        print("\n🔍 测试1：检查处理器的组信息")
        
        if hasattr(app, 'processor') and app.processor:
            processor = app.processor
            print(f"    处理器存在: {type(processor)}")
            
            # 检查组信息属性
            all_groups = getattr(processor, 'all_groups', None)
            groups_info = getattr(processor, 'groups_info', None)
            
            print(f"    all_groups: {type(all_groups)} - {len(all_groups) if all_groups else 0} 个组")
            print(f"    groups_info: {type(groups_info)} - {len(groups_info) if groups_info else 0} 个组信息")
            
            if all_groups:
                print(f"    前3个组的内容:")
                for i, group in enumerate(all_groups[:3]):
                    print(f"      组{i+1}: {len(group) if isinstance(group, list) else 'N/A'} 个实体")
            
            if groups_info:
                print(f"    前3个组信息:")
                for i, info in enumerate(groups_info[:3]):
                    if isinstance(info, dict):
                        status = info.get('status', 'unknown')
                        label = info.get('label', 'unknown')
                        print(f"      组{i+1}: status={status}, label={label}")
        else:
            print(f"    ❌ 处理器不存在")
        
        # 测试2：拦截概览图绘制方法调用
        print("\n🔍 测试2：拦截概览图绘制方法调用")
        
        # 保存原始方法
        original_visualize_overview_with_group_colors = app._visualize_overview_with_group_colors
        original_draw_entities_by_groups = app._draw_entities_by_groups
        original_draw_entities_simplified_group_based = app._draw_entities_simplified_group_based
        
        def traced_visualize_overview_with_group_colors(*args, **kwargs):
            print(f"    📞 _visualize_overview_with_group_colors 被调用")
            print(f"      参数数量: {len(args)}")
            
            if len(args) >= 4:  # processor参数
                processor = args[3]
                if processor:
                    all_groups = getattr(processor, 'all_groups', [])
                    groups_info = getattr(processor, 'groups_info', [])
                    print(f"      传入的组数据: {len(all_groups)} 个组, {len(groups_info)} 个组信息")
                    
                    if all_groups and groups_info:
                        print(f"      ✅ 将调用 _draw_entities_by_groups")
                    else:
                        print(f"      ⚠️ 将调用 _draw_entities_simplified_group_based")
                else:
                    print(f"      ❌ processor 为空")
            
            return original_visualize_overview_with_group_colors(*args, **kwargs)
        
        def traced_draw_entities_by_groups(*args, **kwargs):
            print(f"    📞 _draw_entities_by_groups 被调用")
            print(f"      参数数量: {len(args)}")
            if len(args) >= 3:
                all_entities = args[0] if len(args) > 0 else []
                all_groups = args[1] if len(args) > 1 else []
                groups_info = args[2] if len(args) > 2 else []
                print(f"      实体数量: {len(all_entities)}")
                print(f"      组数量: {len(all_groups)}")
                print(f"      组信息数量: {len(groups_info)}")
            return original_draw_entities_by_groups(*args, **kwargs)
        
        def traced_draw_entities_simplified_group_based(*args, **kwargs):
            print(f"    📞 _draw_entities_simplified_group_based 被调用")
            print(f"      参数数量: {len(args)}")
            if len(args) >= 3:
                all_entities = args[0] if len(args) > 0 else []
                current_group_entities = args[1] if len(args) > 1 else []
                processor = args[2] if len(args) > 2 else None
                print(f"      实体数量: {len(all_entities)}")
                print(f"      当前组实体数量: {len(current_group_entities) if current_group_entities else 0}")
                print(f"      处理器: {type(processor) if processor else None}")
            return original_draw_entities_simplified_group_based(*args, **kwargs)
        
        # 替换方法
        app._visualize_overview_with_group_colors = traced_visualize_overview_with_group_colors
        app._draw_entities_by_groups = traced_draw_entities_by_groups
        app._draw_entities_simplified_group_based = traced_draw_entities_simplified_group_based
        
        # 测试3：模拟概览图调用
        print("\n🔍 测试3：模拟概览图调用")
        
        try:
            # 创建模拟数据
            mock_entities = [
                {'id': 'test1', 'type': 'LINE', 'layer': 'WALL', 'points': [(0, 0), (100, 0)]},
                {'id': 'test2', 'type': 'LINE', 'layer': 'DOOR', 'points': [(50, 0), (50, 100)]}
            ]
            
            print(f"    调用 visualize_overview 方法...")
            app.visualize_overview(
                mock_entities,      # all_entities
                None,               # current_group_entities
                None,               # labeled_entities
                app.processor,      # processor
                None,               # current_group_index
                None,               # wall_fills
                None                # wall_fill_processor
            )
            print(f"    ✅ 概览图调用成功")
            
        except Exception as e:
            print(f"    ❌ 概览图调用失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 恢复原始方法
        app._visualize_overview_with_group_colors = original_visualize_overview_with_group_colors
        app._draw_entities_by_groups = original_draw_entities_by_groups
        app._draw_entities_simplified_group_based = original_draw_entities_simplified_group_based
        
        # 测试4：检查可视化器对象
        print("\n🔍 测试4：检查可视化器对象")
        
        if hasattr(app, 'visualizer') and app.visualizer:
            visualizer = app.visualizer
            print(f"    可视化器存在: {type(visualizer)}")
            
            # 检查可视化器的方法
            if hasattr(visualizer, 'visualize_overview'):
                viz_method = visualizer.visualize_overview
                print(f"    可视化器有 visualize_overview 方法: {viz_method}")
                
                # 检查方法来源
                try:
                    import inspect
                    source_file = inspect.getfile(viz_method)
                    print(f"    方法定义文件: {os.path.basename(source_file)}")
                except:
                    pass
            else:
                print(f"    可视化器没有 visualize_overview 方法")
        else:
            print(f"    ❌ 可视化器不存在")
        
        # 测试5：检查视图切换状态对概览图的影响
        print("\n🔍 测试5：检查视图切换状态对概览图的影响")
        
        current_mode = getattr(app, 'current_view_mode', 'unknown')
        print(f"    当前视图模式: {current_mode}")
        
        # 检查是否有视图模式相关的逻辑影响概览图
        if hasattr(app, '_switch_to_alternative_view'):
            print(f"    有 _switch_to_alternative_view 方法")
        if hasattr(app, '_switch_to_original_view'):
            print(f"    有 _switch_to_original_view 方法")
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 概览图数据流测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_overview_data_flow()
    if success:
        print("\n🎊 概览图数据流测试成功！")
    else:
        print("\n⚠️ 概览图数据流测试发现问题")
