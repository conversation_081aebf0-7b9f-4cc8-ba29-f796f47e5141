#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试融合V2填充功能的独立CAD应用程序
验证所有功能是否正常集成
"""

def test_v2_integration_imports():
    """测试V2集成的导入"""
    print("🔍 测试V2集成导入")
    print("="*50)
    
    try:
        from main_enhanced_standalone import StandaloneCADApp, StandaloneCADProcessor
        print("✅ 主要类导入成功")
        
        # 测试V2相关属性
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        app = StandaloneCADApp(root)
        
        # 检查V2填充相关属性
        v2_attrs = [
            'enhanced_wall_fill_processor_v2',
            'wall_fill_processor_v2',
            'current_wall_fills',
            'current_wall_fill_processor',
            'interactive_fill_mode',
            'interactive_fill_groups',
            'file_combo',
            'file_list',
            'auto_fill_btn',
            'save_fill_btn'
        ]
        
        for attr in v2_attrs:
            if hasattr(app, attr):
                print(f"✅ V2属性 {attr}: 存在")
            else:
                print(f"❌ V2属性 {attr}: 缺失")
        
        # 检查V2方法
        v2_methods = [
            'auto_fill_walls',
            'save_wall_fills',
            '_auto_fill_walls_impl',
            '_unified_wall_filling_process',
            '_identify_wall_groups',
            '_update_visualization_with_fills_v2',
            '_start_interactive_fill',
            'update_file_combo',
            'on_file_selected'
        ]
        
        for method in v2_methods:
            if hasattr(app, method):
                print(f"✅ V2方法 {method}: 存在")
            else:
                print(f"❌ V2方法 {method}: 缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ V2集成导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_v2_wall_fill_functionality():
    """测试V2墙体填充功能"""
    print(f"\n🔍 测试V2墙体填充功能")
    print("="*50)
    
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp, StandaloneCADProcessor
        
        # 创建应用程序
        root = tk.Tk()
        root.withdraw()
        app = StandaloneCADApp(root)
        
        # 创建处理器
        processor = StandaloneCADProcessor()
        app.processor = processor
        
        # 创建测试墙体实体
        wall_entities = [
            {
                'id': 1,
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': 'wall',
                'auto_labeled': True,
                'points': [(0, 0), (100, 0)]
            },
            {
                'id': 2,
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': 'wall',
                'auto_labeled': True,
                'points': [(100, 0), (100, 100)]
            },
            {
                'id': 3,
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': 'wall',
                'auto_labeled': True,
                'points': [(100, 100), (0, 100)]
            },
            {
                'id': 4,
                'type': 'LINE',
                'layer': 'A-WALL',
                'label': 'wall',
                'auto_labeled': True,
                'points': [(0, 100), (0, 0)]
            }
        ]
        
        processor.current_file_entities = wall_entities
        processor.all_groups = [wall_entities]
        
        print(f"✅ 创建测试墙体数据: {len(wall_entities)} 个实体")
        
        # 测试墙体组识别
        wall_groups = app._identify_wall_groups(wall_entities)
        print(f"✅ 墙体组识别: 找到 {len(wall_groups)} 个墙体组")
        
        # 测试基础墙体填充
        if wall_groups:
            fill_result = app._basic_wall_fill(wall_groups[0])
            if fill_result and fill_result.get('fill_polygons'):
                print(f"✅ 基础墙体填充: 成功，生成 {len(fill_result['fill_polygons'])} 个填充多边形")
            else:
                print(f"❌ 基础墙体填充: 失败")
        
        # 测试V2处理器可用性
        if app.enhanced_wall_fill_processor_v2:
            print("✅ 增强V2处理器: 可用")
        elif app.wall_fill_processor_v2:
            print("✅ 标准V2处理器: 可用")
        else:
            print("⚠️ V2处理器: 不可用，将使用基础填充")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ V2墙体填充功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multi_file_functionality():
    """测试多文件处理功能"""
    print(f"\n🔍 测试多文件处理功能")
    print("="*50)
    
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp
        
        # 创建应用程序
        root = tk.Tk()
        root.withdraw()
        app = StandaloneCADApp(root)
        
        # 测试文件列表功能
        test_files = [
            "C:/test/file1.dxf",
            "C:/test/file2.dxf",
            "C:/test/file3.dxf"
        ]
        
        app.file_list = test_files
        print(f"✅ 设置测试文件列表: {len(test_files)} 个文件")
        
        # 测试文件下拉框更新
        if app.file_combo:
            app.update_file_combo()
            print("✅ 文件下拉框更新: 成功")
        else:
            print("⚠️ 文件下拉框: 未初始化")
        
        # 测试文件选择功能
        if hasattr(app, 'on_file_selected'):
            print("✅ 文件选择功能: 存在")
        else:
            print("❌ 文件选择功能: 缺失")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 多文件处理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_integration():
    """测试UI集成"""
    print(f"\n🔍 测试UI集成")
    print("="*50)
    
    try:
        import tkinter as tk
        from main_enhanced_standalone import StandaloneCADApp
        
        # 创建应用程序
        root = tk.Tk()
        root.withdraw()
        app = StandaloneCADApp(root)
        
        # 检查新增的UI组件
        ui_components = [
            ('file_combo', '文件下拉框'),
            ('auto_fill_btn', 'V2墙体填充按钮'),
            ('save_fill_btn', '保存填充按钮')
        ]
        
        for component, name in ui_components:
            if hasattr(app, component) and getattr(app, component):
                print(f"✅ {name}: 存在")
            else:
                print(f"❌ {name}: 缺失")
        
        # 检查房间识别按钮（如果可用）
        if hasattr(app, 'room_btn'):
            print("✅ 房间识别按钮: 存在")
        else:
            print("⚠️ 房间识别按钮: 不存在（可能是模块不可用）")
        
        # 测试按钮状态
        if hasattr(app, 'auto_fill_btn') and app.auto_fill_btn:
            initial_state = app.auto_fill_btn['state']
            print(f"✅ V2填充按钮初始状态: {initial_state}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_availability():
    """测试组件可用性"""
    print(f"\n🔍 测试组件可用性")
    print("="*50)
    
    # 测试各个组件的导入状态
    components = [
        ('ENHANCED_WALL_FILL_V2_AVAILABLE', '增强墙体填充V2'),
        ('OVERVIEW_CACHE_AVAILABLE', '数据缓存系统'),
        ('ROOM_RECOGNITION_AVAILABLE', '房间识别'),
        ('DISPLAY_CONTROLLER_AVAILABLE', '显示控制器'),
        ('SHADOW_GENERATOR_AVAILABLE', '阴影生成器')
    ]
    
    try:
        from main_enhanced_standalone import (
            ENHANCED_WALL_FILL_V2_AVAILABLE,
            OVERVIEW_CACHE_AVAILABLE,
            ROOM_RECOGNITION_AVAILABLE,
            DISPLAY_CONTROLLER_AVAILABLE,
            SHADOW_GENERATOR_AVAILABLE
        )
        
        available_count = 0
        total_count = len(components)
        
        for var_name, component_name in components:
            is_available = globals()[var_name]
            status = "✅ 可用" if is_available else "⚠️ 不可用"
            print(f"{component_name}: {status}")
            if is_available:
                available_count += 1
        
        print(f"\n📊 组件可用性: {available_count}/{total_count} ({available_count/total_count*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件可用性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 启动V2集成独立CAD应用程序测试")
    print("="*60)
    
    # 测试1：V2集成导入测试
    test1_result = test_v2_integration_imports()
    
    # 测试2：V2墙体填充功能测试
    test2_result = test_v2_wall_fill_functionality()
    
    # 测试3：多文件处理功能测试
    test3_result = test_multi_file_functionality()
    
    # 测试4：UI集成测试
    test4_result = test_ui_integration()
    
    # 测试5：组件可用性测试
    test5_result = test_component_availability()
    
    print(f"\n" + "="*60)
    print("📊 V2集成独立CAD应用程序测试结果")
    print("="*60)
    
    print(f"🧪 V2集成导入: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"🧪 V2墙体填充功能: {'✅ 通过' if test2_result else '❌ 失败'}")
    print(f"🧪 多文件处理功能: {'✅ 通过' if test3_result else '❌ 失败'}")
    print(f"🧪 UI集成: {'✅ 通过' if test4_result else '❌ 失败'}")
    print(f"🧪 组件可用性: {'✅ 通过' if test5_result else '❌ 失败'}")
    
    overall_success = test1_result and test2_result and test3_result and test4_result
    
    if overall_success:
        print(f"\n🎉 V2集成测试通过！独立程序功能完整")
        print(f"\n💡 新增功能:")
        print(f"1. V2墙体填充: 增强版和标准版处理器")
        print(f"2. 交互式填充: 逐步控制填充过程")
        print(f"3. 多文件支持: 文件列表和切换功能")
        print(f"4. 房间识别: 智能房间检测（如果可用）")
        print(f"5. 数据缓存: 高效的数据存储和检索")
        print(f"6. 显示控制: 增强的可视化控制")
        
        print(f"\n🔧 使用说明:")
        print(f"1. 启动程序: python main_enhanced_standalone.py")
        print(f"2. 选择包含DXF文件的文件夹")
        print(f"3. 从文件下拉列表选择要处理的文件")
        print(f"4. 点击'开始处理'进行文件分析")
        print(f"5. 使用'V2墙体填充'进行高级墙体填充")
        print(f"6. 选择交互式或自动填充模式")
        print(f"7. 点击'保存填充'保存结果")
        print(f"8. 使用调试功能检查颜色显示")
    else:
        print(f"\n⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
