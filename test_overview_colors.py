#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试概览图颜色获取逻辑
检查概览图是否正确使用基于组信息的颜色，而不是基于图层信息的颜色
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_overview_colors():
    """测试概览图颜色获取逻辑"""
    print("🔍 开始测试概览图颜色获取逻辑...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("概览图颜色测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(1)
        
        # 测试1：检查颜色获取方法的存在性
        print("\n🔍 测试1：检查颜色获取方法的存在性")
        
        required_methods = [
            '_get_group_color_for_overview',
            '_get_group_color_fallback',
            '_visualize_overview_with_group_colors',
            '_draw_entities_by_groups',
            '_draw_entities_simplified_group_based'
        ]
        
        for method_name in required_methods:
            if hasattr(app, method_name):
                print(f"    ✅ {method_name} - 存在")
            else:
                print(f"    ❌ {method_name} - 缺失")
        
        # 测试2：检查替代视图器的存在性
        print("\n🔍 测试2：检查替代视图器的存在性")
        
        if hasattr(app, 'alternative_visualizer'):
            alt_viz = app.alternative_visualizer
            if alt_viz:
                print(f"    ✅ alternative_visualizer 存在: {type(alt_viz)}")
                
                # 检查替代视图器的关键方法
                alt_methods = [
                    'get_alternative_entity_color',
                    'get_color_by_group_info'
                ]
                
                for method_name in alt_methods:
                    if hasattr(alt_viz, method_name):
                        print(f"      ✅ {method_name} - 存在")
                    else:
                        print(f"      ❌ {method_name} - 缺失")
            else:
                print(f"    ❌ alternative_visualizer 为空")
        else:
            print(f"    ❌ alternative_visualizer 属性不存在")
        
        # 测试3：模拟组信息和实体数据
        print("\n🔍 测试3：模拟组信息和实体数据")
        
        # 创建模拟的组信息
        mock_groups_info = [
            {
                'status': 'labeling',
                'label': '墙体',
                'group_index': 0
            },
            {
                'status': 'labeled',
                'label': '门窗',
                'group_index': 1
            },
            {
                'status': 'unlabeled',
                'label': '未标注',
                'group_index': 2
            }
        ]
        
        # 创建模拟的实体数据
        mock_entity = {
            'id': 'test_entity_1',
            'type': 'LINE',
            'layer': 'WALL',  # 图层信息
            'points': [(0, 0), (100, 0)]
        }
        
        print(f"    模拟组信息: {len(mock_groups_info)} 个组")
        print(f"    模拟实体: {mock_entity}")
        
        # 测试4：测试颜色获取逻辑
        print("\n🔍 测试4：测试颜色获取逻辑")
        
        for i, group_info in enumerate(mock_groups_info):
            print(f"\n  测试组 {i+1}: {group_info}")
            
            try:
                # 测试概览图颜色获取
                if hasattr(app, '_get_group_color_for_overview'):
                    color = app._get_group_color_for_overview(group_info, mock_entity, None)
                    print(f"    概览图颜色: {color}")
                
                # 测试备用颜色获取
                if hasattr(app, '_get_group_color_fallback'):
                    fallback_color = app._get_group_color_fallback(group_info)
                    print(f"    备用颜色: {fallback_color}")
                
                # 测试替代视图器颜色获取
                if hasattr(app, 'alternative_visualizer') and app.alternative_visualizer:
                    if hasattr(app.alternative_visualizer, 'get_alternative_entity_color'):
                        alt_color = app.alternative_visualizer.get_alternative_entity_color(None, group_info)
                        print(f"    替代视图颜色: {alt_color}")
                
            except Exception as e:
                print(f"    ❌ 颜色获取失败: {e}")
        
        # 测试5：检查概览图绘制方法
        print("\n🔍 测试5：检查概览图绘制方法")
        
        # 检查当前使用的概览图方法
        if hasattr(app, 'visualize_overview'):
            viz_method = app.visualize_overview
            print(f"    当前概览图方法: {viz_method}")
            print(f"    方法类型: {type(viz_method)}")
            
            # 检查是否为修复版本
            if hasattr(viz_method, '__name__'):
                method_name = viz_method.__name__
                print(f"    方法名称: {method_name}")
                
                if 'group' in method_name.lower() or 'alternative' in method_name.lower():
                    print(f"    ✅ 使用基于组信息的方法")
                else:
                    print(f"    ⚠️ 可能使用基于图层信息的方法")
        
        # 测试6：检查视图切换状态
        print("\n🔍 测试6：检查视图切换状态")
        
        current_mode = getattr(app, 'current_view_mode', 'unknown')
        print(f"    当前视图模式: {current_mode}")
        
        if current_mode == 'alternative':
            print(f"    ✅ 当前使用替代视图（基于组信息）")
        elif current_mode == 'original':
            print(f"    ⚠️ 当前使用原始视图（可能基于图层信息）")
        else:
            print(f"    ❌ 视图模式未知")
        
        # 测试7：模拟概览图绘制
        print("\n🔍 测试7：模拟概览图绘制")
        
        try:
            # 创建模拟的处理器
            class MockProcessor:
                def __init__(self):
                    self.all_groups = [
                        [mock_entity],  # 第一组包含测试实体
                        [],             # 第二组为空
                        []              # 第三组为空
                    ]
                    self.groups_info = mock_groups_info
            
            mock_processor = MockProcessor()
            
            # 测试概览图绘制方法
            if hasattr(app, '_visualize_overview_with_group_colors'):
                print(f"    调用 _visualize_overview_with_group_colors...")
                app._visualize_overview_with_group_colors(
                    [mock_entity],      # all_entities
                    None,               # current_group_entities
                    None,               # labeled_entities
                    mock_processor,     # processor
                    None,               # current_group_index
                    None,               # wall_fills
                    None                # wall_fill_processor
                )
                print(f"    ✅ 概览图绘制方法调用成功")
            else:
                print(f"    ❌ _visualize_overview_with_group_colors 方法不存在")
        
        except Exception as e:
            print(f"    ❌ 模拟概览图绘制失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 概览图颜色获取逻辑测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_overview_colors()
    if success:
        print("\n🎊 概览图颜色测试成功！")
        print("✅ 颜色获取逻辑正常工作")
    else:
        print("\n⚠️ 概览图颜色测试发现问题")
        print("❌ 需要检查颜色获取逻辑")
