#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试V2程序完整启动过程
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_v2_startup():
    """测试V2程序启动"""
    print("🚀 测试V2程序启动...")
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ 主应用模块导入成功")
        
        # 创建根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口以避免显示
        print("✅ Tkinter根窗口创建成功")
        
        # 创建应用实例
        print("🔄 正在创建应用实例...")
        app = EnhancedCADAppV2(root)
        print("✅ 应用实例创建成功")
        
        # 检查房间识别模块
        if hasattr(app, 'room_processor'):
            if app.room_processor:
                print("✅ 房间识别处理器已初始化")
            else:
                print("⚠️ 房间识别处理器为None（可能是导入失败）")
        else:
            print("❌ 房间识别处理器属性不存在")
            return False
        
        if hasattr(app, 'room_ui'):
            print("✅ 房间识别UI属性已创建")
        else:
            print("❌ 房间识别UI属性不存在")
            return False
        
        # 检查其他关键属性
        key_attributes = [
            'wall_fill_processor_v2',
            'group_fill_status',
            'hidden_groups',
            'current_folder',
            'current_file',
            'file_status'
        ]
        
        for attr in key_attributes:
            if hasattr(app, attr):
                print(f"✅ {attr} 属性存在")
            else:
                print(f"❌ {attr} 属性缺失")
                return False
        
        # 检查房间识别相关方法
        room_methods = [
            '_pre_init_room_recognition',
            '_complete_room_recognition_init',
            '_create_room_recognition_panel',
            '_get_wall_door_data',
            '_on_room_update'
        ]
        
        for method in room_methods:
            if hasattr(app, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
                return False
        
        # 销毁窗口
        root.destroy()
        print("✅ 测试窗口已销毁")
        
        return True
        
    except Exception as e:
        print(f"❌ V2程序启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_room_recognition_integration():
    """测试房间识别模块集成"""
    print("\n🏠 测试房间识别模块集成...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建测试应用
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        
        # 测试房间识别功能
        if app.room_processor:
            # 测试基本功能
            wall_groups = [
                [
                    {'type': 'LINE', 'start': {'x': 0, 'y': 0}, 'end': {'x': 100, 'y': 0}},
                    {'type': 'LINE', 'start': {'x': 100, 'y': 0}, 'end': {'x': 100, 'y': 100}},
                    {'type': 'LINE', 'start': {'x': 100, 'y': 100}, 'end': {'x': 0, 'y': 100}},
                    {'type': 'LINE', 'start': {'x': 0, 'y': 100}, 'end': {'x': 0, 'y': 0}}
                ]
            ]
            
            door_groups = []
            
            result = app.room_processor.process_room_recognition(wall_groups, door_groups)
            
            if result:
                print("✅ 房间识别功能正常工作")
                print(f"   建筑外轮廓: {'已识别' if result['building_outline'] else '未识别'}")
                print(f"   房间数量: {len(result['rooms'])}")
            else:
                print("⚠️ 房间识别返回空结果")
            
            # 测试数据获取方法
            wall_data, door_data = app._get_wall_door_data()
            print(f"✅ 数据获取方法正常: 墙体组{len(wall_data)}, 门窗组{len(door_data)}")
            
        else:
            print("⚠️ 房间识别处理器未初始化，跳过功能测试")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 房间识别集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_creation():
    """测试UI创建"""
    print("\n🖼️ 测试UI创建...")
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建可见的测试窗口
        root = tk.Tk()
        root.title("V2程序启动测试")
        root.geometry("800x600")
        
        print("🔄 正在创建应用UI...")
        app = EnhancedCADAppV2(root)
        print("✅ 应用UI创建成功")
        
        # 检查主要UI组件
        if hasattr(app, 'root') and app.root:
            print("✅ 根窗口正常")
        
        # 添加测试信息
        info_frame = tk.Frame(root, bg='lightblue', height=50)
        info_frame.pack(fill='x', side='bottom')
        info_frame.pack_propagate(False)
        
        info_label = tk.Label(info_frame, 
                             text="✅ V2程序启动成功！房间识别模块已集成。点击关闭按钮退出测试。",
                             bg='lightblue', font=('Arial', 10, 'bold'))
        info_label.pack(expand=True)
        
        # 添加关闭按钮
        close_btn = tk.Button(info_frame, text="关闭测试", 
                             command=root.destroy,
                             bg='red', fg='white', font=('Arial', 10, 'bold'))
        close_btn.pack(side='right', padx=10, pady=5)
        
        print("✅ UI测试界面准备完成")
        print("   请检查界面是否正常显示，然后关闭窗口")
        
        # 运行界面（这会阻塞直到窗口关闭）
        root.mainloop()
        
        print("✅ UI测试完成")
        return True
        
    except Exception as e:
        print(f"❌ UI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始V2程序完整启动测试...")
    print("=" * 80)
    
    # 执行测试
    tests = [
        ("V2程序启动", test_v2_startup),
        ("房间识别集成", test_room_recognition_integration),
        ("UI创建", test_ui_creation)  # UI测试放在最后
    ]
    
    all_passed = True
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                all_passed = False
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 V2程序启动测试全部通过！")
        print("\n📝 测试结果:")
        print("1. ✅ 主应用模块导入正常")
        print("2. ✅ 应用实例创建成功")
        print("3. ✅ 房间识别模块正确初始化")
        print("4. ✅ 所有关键属性和方法存在")
        print("5. ✅ 房间识别功能正常工作")
        print("6. ✅ UI界面正常创建和显示")
        
        print("\n🎯 V2程序现在可以正常启动和运行！")
    else:
        print("❌ 部分测试失败，请检查问题。")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
