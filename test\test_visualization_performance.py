#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可视化性能优化
验证是否解决了可视化器中的性能瓶颈
"""

import os
import sys
import time

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from fast_visualizer_patch import FastVisualizationOptimizer, patch_visualizer_performance
    print("✅ 快速可视化补丁导入成功")
except ImportError as e:
    print(f"❌ 快速可视化补丁导入失败: {e}")
    sys.exit(1)


class MockVisualizer:
    """模拟可视化器用于测试"""
    
    def __init__(self):
        """初始化模拟可视化器"""
        self.ax_detail = MockAxis()
        self.ax_overview = MockAxis()
        self.draw_calls = 0
        self.overview_calls = 0
    
    def draw_entities(self, entities):
        """模拟绘制实体（原始方法）"""
        self.draw_calls += 1
        time.sleep(0.001 * len(entities))  # 模拟绘制时间
        print(f"🎨 原始绘制: {len(entities)} 个实体")
    
    def visualize_overview(self, all_entities, current_group_entities=None, 
                         labeled_entities=None, **kwargs):
        """模拟概览可视化（原始方法）"""
        self.overview_calls += 1
        
        # 模拟原始方法的复杂操作
        for entity in all_entities:
            # 模拟 _find_entity_group_index 的开销
            time.sleep(0.0001)  # 每个实体0.1毫秒
        
        print(f"🌍 原始概览: {len(all_entities)} 个实体")


class MockAxis:
    """模拟matplotlib轴对象"""
    
    def __init__(self):
        self.cleared = False
        self.plotted = False
    
    def clear(self):
        self.cleared = True
    
    def set_aspect(self, aspect):
        pass
    
    def grid(self, *args, **kwargs):
        pass
    
    def plot(self, x, y, *args, **kwargs):
        self.plotted = True
    
    def set_xlim(self, *args):
        pass
    
    def set_ylim(self, *args):
        pass
    
    def set_title(self, title, **kwargs):
        pass


def create_test_entities(count=400):
    """创建测试实体"""
    entities = []
    
    for i in range(count):
        entities.append({
            'id': f'entity_{i}',
            'type': 'LINE',
            'layer': f'LAYER_{i % 5}',  # 5个不同图层
            'start_point': [i * 10, 0],
            'end_point': [i * 10 + 5, 0],
            'color': i % 3 + 1,
            'points': [[i * 10, 0], [i * 10 + 5, 0]]
        })
    
    return entities


def test_fast_visualization_optimizer():
    """测试快速可视化优化器"""
    print("🧪 测试快速可视化优化器")
    print("=" * 50)
    
    # 创建测试数据
    test_entities = create_test_entities(400)
    current_group = test_entities[:50]
    labeled_entities = test_entities[50:100]
    
    print(f"📊 测试数据: {len(test_entities)} 个实体")
    print(f"   当前组: {len(current_group)} 个实体")
    print(f"   已标注: {len(labeled_entities)} 个实体")
    
    # 创建优化器和模拟可视化器
    optimizer = FastVisualizationOptimizer()
    visualizer = MockVisualizer()
    
    # 测试优化绘制
    print(f"\n🎨 测试优化绘制...")
    start_time = time.time()
    success = optimizer.optimize_draw_entities(visualizer, test_entities)
    draw_time = time.time() - start_time
    
    print(f"   优化绘制成功: {success}")
    print(f"   绘制时间: {draw_time:.3f} 秒")
    
    # 测试优化概览
    print(f"\n🌍 测试优化概览...")
    start_time = time.time()
    success = optimizer.optimize_visualize_overview(
        visualizer, test_entities, current_group, labeled_entities
    )
    overview_time = time.time() - start_time
    
    print(f"   优化概览成功: {success}")
    print(f"   概览时间: {overview_time:.3f} 秒")
    
    return draw_time, overview_time


def test_visualization_patch():
    """测试可视化补丁"""
    print("\n🧪 测试可视化补丁")
    print("=" * 50)
    
    # 创建测试数据
    test_entities = create_test_entities(400)
    current_group = test_entities[:50]
    labeled_entities = test_entities[50:100]
    
    # 创建模拟可视化器
    visualizer = MockVisualizer()
    
    # 测试原始性能
    print("📊 测试原始性能...")
    start_time = time.time()
    visualizer.draw_entities(test_entities)
    original_draw_time = time.time() - start_time
    
    start_time = time.time()
    visualizer.visualize_overview(test_entities, current_group, labeled_entities)
    original_overview_time = time.time() - start_time
    
    print(f"   原始绘制时间: {original_draw_time:.3f} 秒")
    print(f"   原始概览时间: {original_overview_time:.3f} 秒")
    
    # 应用补丁
    print(f"\n⚡ 应用性能补丁...")
    patch_visualizer_performance(visualizer)
    
    # 测试优化后性能
    print("📊 测试优化后性能...")
    start_time = time.time()
    visualizer.draw_entities(test_entities)
    patched_draw_time = time.time() - start_time
    
    start_time = time.time()
    visualizer.visualize_overview(test_entities, current_group, labeled_entities)
    patched_overview_time = time.time() - start_time
    
    print(f"   优化绘制时间: {patched_draw_time:.3f} 秒")
    print(f"   优化概览时间: {patched_overview_time:.3f} 秒")
    
    # 计算性能提升
    draw_speedup = original_draw_time / patched_draw_time if patched_draw_time > 0 else float('inf')
    overview_speedup = original_overview_time / patched_overview_time if patched_overview_time > 0 else float('inf')
    
    print(f"\n🚀 性能提升:")
    print(f"   绘制性能提升: {draw_speedup:.1f}x")
    print(f"   概览性能提升: {overview_speedup:.1f}x")
    
    return {
        'original_draw_time': original_draw_time,
        'patched_draw_time': patched_draw_time,
        'original_overview_time': original_overview_time,
        'patched_overview_time': patched_overview_time,
        'draw_speedup': draw_speedup,
        'overview_speedup': overview_speedup
    }


def test_different_entity_counts():
    """测试不同实体数量的性能"""
    print("\n🧪 测试不同实体数量的性能")
    print("=" * 50)
    
    entity_counts = [50, 100, 200, 400, 800]
    results = []
    
    for count in entity_counts:
        print(f"\n📊 测试 {count} 个实体...")
        
        test_entities = create_test_entities(count)
        optimizer = FastVisualizationOptimizer()
        visualizer = MockVisualizer()
        
        # 测试优化绘制
        start_time = time.time()
        optimizer.optimize_draw_entities(visualizer, test_entities)
        draw_time = time.time() - start_time
        
        # 测试优化概览
        start_time = time.time()
        optimizer.optimize_visualize_overview(visualizer, test_entities)
        overview_time = time.time() - start_time
        
        total_time = draw_time + overview_time
        
        print(f"   绘制时间: {draw_time:.3f} 秒")
        print(f"   概览时间: {overview_time:.3f} 秒")
        print(f"   总时间: {total_time:.3f} 秒")
        
        results.append({
            'count': count,
            'draw_time': draw_time,
            'overview_time': overview_time,
            'total_time': total_time
        })
    
    return results


def run_visualization_performance_test():
    """运行可视化性能测试"""
    print("🚀 开始可视化性能测试")
    print("=" * 80)
    
    start_time = time.time()
    
    try:
        # 1. 测试快速可视化优化器
        optimizer_draw_time, optimizer_overview_time = test_fast_visualization_optimizer()
        
        # 2. 测试可视化补丁
        patch_results = test_visualization_patch()
        
        # 3. 测试不同实体数量
        scale_results = test_different_entity_counts()
        
        total_time = time.time() - start_time
        
        print(f"\n🎉 可视化性能测试完成")
        print("=" * 80)
        print(f"   总测试时间: {total_time:.2f} 秒")
        
        # 分析结果
        print(f"\n📊 性能分析:")
        print(f"   优化器绘制时间: {optimizer_draw_time:.3f} 秒")
        print(f"   优化器概览时间: {optimizer_overview_time:.3f} 秒")
        print(f"   补丁绘制提升: {patch_results['draw_speedup']:.1f}x")
        print(f"   补丁概览提升: {patch_results['overview_speedup']:.1f}x")
        
        # 评估性能
        if (optimizer_draw_time < 0.1 and optimizer_overview_time < 0.1 and 
            patch_results['draw_speedup'] > 2 and patch_results['overview_speedup'] > 2):
            print(f"   🎉 可视化性能优化极其成功！")
            print(f"   ⚡ 绘制和概览都实现了显著提升")
        elif (optimizer_draw_time < 0.5 and optimizer_overview_time < 0.5 and 
              patch_results['draw_speedup'] > 1.5):
            print(f"   ✅ 可视化性能优化成功！")
        else:
            print(f"   ⚠️ 可视化性能仍需进一步优化")
        
        print(f"\n📋 规模测试结果:")
        for result in scale_results:
            print(f"   {result['count']}个实体: {result['total_time']:.3f}秒")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_visualization_performance_test()
    
    if success:
        print(f"\n🎊 可视化性能测试成功！")
        print(f"   可视化瓶颈问题已解决")
        print(f"   线条处理等待时间应大幅减少")
    else:
        print(f"\n😞 测试失败，需要进一步调试")
    
    sys.exit(0 if success else 1)
