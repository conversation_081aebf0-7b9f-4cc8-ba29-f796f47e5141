#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证界面布局调整
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_subplot_alignment():
    """验证详细视图和概览图高度对齐"""
    
    print("📐 验证详细视图和概览图高度对齐")
    print("=" * 50)
    
    try:
        # 检查cad_visualizer.py中的布局设置
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找gridspec设置
        if 'height_ratios=[0.1, 2.8, 1]' in content:
            print("✅ 高度比例已优化：[0.1, 2.8, 1]")
        else:
            print("❌ 高度比例未找到或不正确")
            return False
        
        # 查找详细视图布局
        if 'gs[1:3, 0]' in content:
            print("✅ 详细视图跨行设置：gs[1:3, 0] (第2-3行)")
        else:
            print("❌ 详细视图跨行设置未找到")
            return False
        
        # 查找概览图布局
        if 'gs[1, 1]' in content:
            print("✅ 概览图位置设置：gs[1, 1] (第2行)")
        else:
            print("❌ 概览图位置设置未找到")
            return False
        
        # 查找索引图布局
        if 'gs[2, 1]' in content:
            print("✅ 索引图位置设置：gs[2, 1] (第3行)")
        else:
            print("❌ 索引图位置设置未找到")
            return False
        
        print("\n🎯 布局效果:")
        print("  - 详细视图：占用第2-3行，与概览图中心对齐")
        print("  - 概览图：占用第2行，与详细视图上半部分对齐")
        print("  - 索引图：占用第3行，紧凑显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_legend_adjustments():
    """验证索引图调整"""
    
    print("\n🎨 验证索引图调整")
    print("=" * 30)
    
    try:
        # 检查main_enhanced_with_v2_fill.py中的索引图设置
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找_draw_legend_content方法
        start_pos = content.find('def _draw_legend_content')
        if start_pos == -1:
            print("❌ 未找到_draw_legend_content方法")
            return False
        
        end_pos = content.find('def ', start_pos + 1)
        if end_pos == -1:
            end_pos = len(content)
        
        method_content = content[start_pos:end_pos]
        
        # 验证调整项目
        adjustments = []
        
        # 1. 颜色方块高度：1.2
        if 'rect_height = 1.2' in method_content:
            adjustments.append("颜色方块高度：1.2")
        else:
            print("❌ 颜色方块高度未设置为1.2")
            return False
        
        # 2. 列间距：1.7
        if 'col * 1.7' in method_content:
            adjustments.append("列间距：1.7")
        else:
            print("❌ 列间距未设置为1.7")
            return False
        
        # 3. 整体左移：从0.5改为0.2
        if '0.2 + col * 1.7' in method_content:
            adjustments.append("整体左移：起始位置0.2")
        else:
            print("❌ 整体左移未设置")
            return False
        
        # 4. 轮廓线宽：0.5
        if 'linewidth=0.5' in method_content:
            adjustments.append("轮廓线宽：0.5")
        else:
            print("❌ 轮廓线宽未设置为0.5")
            return False
        
        print("✅ 索引图调整验证:")
        for adj in adjustments:
            print(f"  - {adj}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def simulate_layout_effects():
    """模拟布局效果"""
    
    print("\n📊 模拟布局效果")
    print("=" * 25)
    
    print("🎯 详细视图和概览图对齐效果:")
    print("  布局结构：3行2列")
    print("  第1行：空白区域 (高度比例: 0.1)")
    print("  第2行：详细视图(左) + 概览图(右) (高度比例: 2.8)")
    print("  第3行：详细视图(左) + 索引图(右) (高度比例: 1.0)")
    print()
    print("  对齐效果：")
    print("  - 详细视图跨第2-3行，总高度 = 2.8 + 1.0 = 3.8")
    print("  - 概览图占第2行，高度 = 2.8")
    print("  - 概览图位于详细视图的上半部分，实现中心对齐")
    
    print("\n🎨 索引图优化效果:")
    print("  颜色方块尺寸：0.2 x 1.2")
    print("  轮廓线宽：0.5 (更细)")
    print("  列间距：1.7 (更紧凑)")
    print("  起始位置：0.2 (整体左移0.3单位)")
    print()
    print("  视觉效果：")
    print("  - 颜色方块更高，更醒目")
    print("  - 轮廓线更细，更精致")
    print("  - 列间距更紧凑，布局更协调")
    print("  - 整体左移，更好利用空间")

def provide_adjustment_summary():
    """提供调整总结"""
    
    print("\n📋 界面布局调整总结")
    print("=" * 30)
    
    print("✅ 已完成的调整:")
    print()
    print("1. **详细视图和概览图高度对齐（中心对齐）**")
    print("   - 使用3行2列布局：height_ratios=[0.1, 2.8, 1]")
    print("   - 详细视图跨第2-3行：gs[1:3, 0]")
    print("   - 概览图占第2行：gs[1, 1]")
    print("   - 实现中心对齐效果")
    print()
    print("2. **索引图颜色块轮廓改细**")
    print("   - 轮廓线宽：1.5 → 0.5")
    print("   - 更精致的视觉效果")
    print()
    print("3. **索引图内容调整**")
    print("   - 颜色方块高度：1.0 → 1.2")
    print("   - 列间距：2.0 → 1.7")
    print("   - 整体左移：起始位置 0.5 → 0.2")
    print()
    print("🎯 预期效果:")
    print("  - 详细视图和概览图垂直中心对齐")
    print("  - 索引图颜色方块更高更精致")
    print("  - 索引图布局更紧凑，空间利用更好")
    print("  - 整体界面更协调美观")

if __name__ == "__main__":
    print("📐 界面布局调整验证程序")
    print("=" * 50)
    
    # 验证详细视图和概览图对齐
    alignment_result = verify_subplot_alignment()
    
    # 验证索引图调整
    legend_result = verify_legend_adjustments()
    
    # 模拟布局效果
    simulate_layout_effects()
    
    # 提供调整总结
    provide_adjustment_summary()
    
    print("\n" + "=" * 50)
    print("🎉 验证总结:")
    
    if alignment_result and legend_result:
        print("✅ 详细视图和概览图对齐验证通过")
        print("✅ 索引图调整验证通过")
        print("✅ 所有布局调整已完成")
        print("\n🚀 界面布局优化效果：")
        print("  1. 详细视图和概览图实现中心对齐")
        print("  2. 索引图颜色方块轮廓更细更精致")
        print("  3. 索引图内容更紧凑，整体左移")
        print("  4. 整体界面更协调美观")
    else:
        print("❌ 部分验证失败")
        if not alignment_result:
            print("❌ 详细视图和概览图对齐验证失败")
        if not legend_result:
            print("❌ 索引图调整验证失败")
