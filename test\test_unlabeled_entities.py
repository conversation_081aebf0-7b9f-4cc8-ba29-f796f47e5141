#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试没有标签的实体
验证Tab10颜色在什么情况下会被使用
"""

import os
import sys
import time
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unlabeled_entities():
    """测试没有标签的实体"""
    print("🔍 测试没有标签的实体")
    print("=" * 80)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建根窗口
        root = tk.Tk()
        root.title("无标签实体测试")
        root.geometry("1400x900")
        
        print("✅ 创建根窗口成功")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 更新界面确保初始化完成
        root.update()
        time.sleep(1)
        
        print("\n🔧 测试1：创建完全没有标签的实体")
        print("-" * 60)
        
        # 创建没有任何标签信息的实体
        unlabeled_entities = []
        entity_id = 1
        
        for i in range(3):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': '0',  # 默认图层
                'start': [i * 50, 0],
                'end': [i * 50 + 40, 0],
                'length': 40,
                'color': 7  # 默认颜色
                # 注意：没有label和auto_labeled字段
            }
            unlabeled_entities.append(entity)
            entity_id += 1
        
        # 创建label为None的实体
        none_labeled_entities = []
        for i in range(2):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': '0',
                'start': [i * 30, 60],
                'end': [i * 30 + 25, 60],
                'length': 25,
                'label': None,  # 明确设置为None
                'auto_labeled': False,
                'color': 8
            }
            none_labeled_entities.append(entity)
            entity_id += 1
        
        # 创建label为字符串'None'的实体
        string_none_entities = []
        for i in range(2):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': '0',
                'start': [i * 25, 120],
                'end': [i * 25 + 20, 120],
                'length': 20,
                'label': 'None',  # 字符串'None'
                'auto_labeled': False,
                'color': 9
            }
            string_none_entities.append(entity)
            entity_id += 1
        
        all_groups = [unlabeled_entities, none_labeled_entities, string_none_entities]
        all_entities = unlabeled_entities + none_labeled_entities + string_none_entities
        
        # 设置处理器数据（关键：不设置groups_info）
        app.processor.raw_entities = all_entities
        app.processor.current_file_entities = all_entities.copy()
        app.processor.all_groups = all_groups
        app.processor.groups_info = []  # 空的groups_info
        app.processor.auto_labeled_entities = []
        app.processor.labeled_entities = []
        
        print(f"✅ 测试数据创建完成:")
        print(f"   完全无标签组: {len(unlabeled_entities)} 个实体")
        print(f"   label=None组: {len(none_labeled_entities)} 个实体")
        print(f"   label='None'组: {len(string_none_entities)} 个实体")
        print(f"   groups_info: 空列表")
        
        print("\n📊 测试每种情况下的颜色分配:")
        print("-" * 60)
        
        # 计算Tab10颜色作为参考
        import matplotlib.pyplot as plt
        import numpy as np
        
        colors = plt.cm.tab10(np.linspace(0, 1, len(all_groups)))
        tab10_colors = []
        for i, color_rgba in enumerate(colors):
            r, g, b = color_rgba[:3]
            hex_color = f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"
            tab10_colors.append(hex_color)
        
        print(f"Tab10颜色参考:")
        for i, color in enumerate(tab10_colors):
            print(f"   组{i+1}: {color}")
        
        # 测试每个组的颜色分配
        for group_idx, group in enumerate(all_groups):
            if not group:
                continue
            
            first_entity = group[0]
            group_name = ["完全无标签", "label=None", "label='None'"][group_idx]
            
            print(f"\n📋 组 {group_idx + 1} ({group_name}):")
            print(f"   实体信息: {first_entity}")
            
            # 测试_get_entity_display_info_enhanced的行为
            try:
                actual_color, alpha, linewidth, status = app.visualizer._get_entity_display_info_enhanced(
                    first_entity,
                    labeled_entities=[],
                    current_group_entities=[],
                    all_groups=all_groups,
                    groups_info=[],  # 空的groups_info
                    processor=app.processor
                )
                print(f"   _get_entity_display_info_enhanced返回:")
                print(f"     颜色: {actual_color}")
                print(f"     状态: {status}")
                print(f"     透明度: {alpha}")
                
                # 检查是否使用了Tab10颜色
                expected_tab10 = tab10_colors[group_idx]
                if actual_color.lower() == expected_tab10.lower():
                    print(f"   ✅ 使用了Tab10颜色: {expected_tab10}")
                else:
                    print(f"   ❌ 未使用Tab10颜色")
                    print(f"     期望: {expected_tab10}")
                    print(f"     实际: {actual_color}")
                    
            except Exception as e:
                print(f"   ❌ 获取颜色失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n🔧 测试2：模拟draw_groups的回退逻辑")
        print("-" * 60)
        
        # 直接测试draw_groups方法的逻辑
        print("模拟draw_groups方法中的颜色分配:")
        
        for group_idx, group in enumerate(all_groups):
            if not group:
                continue
            
            first_entity = group[0]
            group_name = ["完全无标签", "label=None", "label='None'"][group_idx]
            
            print(f"\n📋 组 {group_idx + 1} ({group_name}):")
            
            # 模拟第1376-1377行：计算Tab10颜色
            group_color_tab10 = colors[group_idx % len(colors)]
            r, g, b = group_color_tab10[:3]
            tab10_hex = f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"
            print(f"   第1376-1377行Tab10颜色: {tab10_hex}")
            
            # 模拟第1385-1393行：调用_get_entity_display_info_enhanced
            try:
                if hasattr(app.visualizer, '_get_entity_display_info_enhanced'):
                    color, alpha, linewidth, status = app.visualizer._get_entity_display_info_enhanced(
                        first_entity,
                        labeled_entities=[],
                        current_group_entities=[],
                        all_groups=all_groups,
                        groups_info=[],  # 关键：空的groups_info
                        processor=app.processor
                    )
                    print(f"   第1385-1393行返回颜色: {color}")
                    final_color = color
                else:
                    # 回退逻辑
                    color = tab10_hex
                    alpha = 0.7
                    linewidth = 1.5
                    print(f"   使用回退逻辑，颜色: {color}")
                    final_color = color
                
                print(f"   最终绘制颜色: {final_color}")
                
                if final_color.lower() == tab10_hex.lower():
                    print(f"   ✅ 最终使用Tab10颜色")
                else:
                    print(f"   ❌ 最终未使用Tab10颜色")
                    
            except Exception as e:
                print(f"   ❌ 模拟失败: {e}")
        
        print("\n🔧 测试3：验证我们的假设")
        print("-" * 60)
        
        print("基于测试结果的结论:")
        
        # 检查是否有任何组使用了Tab10颜色
        tab10_used = False
        for group_idx, group in enumerate(all_groups):
            if group:
                first_entity = group[0]
                try:
                    actual_color, _, _, _ = app.visualizer._get_entity_display_info_enhanced(
                        first_entity, [], [], all_groups, [], app.processor
                    )
                    expected_tab10 = tab10_colors[group_idx]
                    if actual_color.lower() == expected_tab10.lower():
                        tab10_used = True
                        break
                except:
                    pass
        
        if tab10_used:
            print("✅ 找到了使用Tab10颜色的情况")
            print("   这说明在某些条件下确实会使用Tab10颜色")
        else:
            print("❌ 即使在极端情况下也没有使用Tab10颜色")
            print("   这说明问题可能在其他地方")
        
        print(f"\n🎉 无标签实体测试完成！")
        print("=" * 80)
        
        # 保持窗口打开一段时间
        print("\n⏰ 窗口将在3秒后关闭...")
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        try:
            root.destroy()
        except:
            pass
        return False

if __name__ == "__main__":
    print("🚀 开始测试没有标签的实体")
    
    success = test_unlabeled_entities()
    
    if success:
        print("\n✅ 测试完成")
        print("现在我们应该能够确定Tab10颜色的使用条件")
    else:
        print("\n❌ 测试失败")
