#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试可折叠索引图
验证索引图的折叠/展开功能和布局效果
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_collapsible_legend():
    """测试可折叠索引图功能"""
    print("🔍 开始测试可折叠索引图...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("可折叠索引图测试")
        root.geometry("1400x900")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        time.sleep(2)
        
        # 测试1：检查可折叠索引图组件是否存在
        print("\n🔍 测试1：检查可折叠索引图组件")
        
        components = [
            ('legend_container', '索引图容器'),
            ('legend_toggle_btn', '折叠/展开按钮'),
            ('legend_collapsed', '折叠状态变量'),
            ('legend_status_label', '状态标签'),
            ('legend_content_frame', '内容框架')
        ]
        
        for attr_name, component_name in components:
            if hasattr(app, attr_name):
                component = getattr(app, attr_name)
                print(f"    ✅ {component_name}: {type(component)}")
            else:
                print(f"    ❌ {component_name}: 不存在")
        
        # 测试2：检查索引图的初始状态
        print("\n🔍 测试2：检查索引图初始状态")
        
        if hasattr(app, 'legend_collapsed'):
            initial_state = app.legend_collapsed.get()
            print(f"    初始折叠状态: {'折叠' if initial_state else '展开'}")
            
            if hasattr(app, 'legend_toggle_btn'):
                btn_text = app.legend_toggle_btn.cget('text')
                print(f"    按钮文本: '{btn_text}'")
                
            if hasattr(app, 'legend_status_label'):
                status_text = app.legend_status_label.cget('text')
                print(f"    状态标签: '{status_text}'")
        
        # 测试3：检查索引图容器的位置和大小
        print("\n🔍 测试3：检查索引图容器位置和大小")
        
        if hasattr(app, 'legend_container'):
            container = app.legend_container
            try:
                width = container.winfo_width()
                height = container.winfo_height()
                x = container.winfo_x()
                y = container.winfo_y()
                print(f"    索引图容器: {width}x{height} at ({x}, {y})")
                
                # 检查是否在图像预览区域底部
                if hasattr(app, 'detail_frame'):
                    detail_frame = app.detail_frame
                    detail_height = detail_frame.winfo_height()
                    detail_y = detail_frame.winfo_y()
                    
                    print(f"    图像预览区域: 高度{detail_height}, Y位置{detail_y}")
                    
                    # 检查索引图是否在底部
                    if y > detail_y:
                        print(f"    ✅ 索引图位于图像预览区域底部")
                    else:
                        print(f"    ⚠️ 索引图位置可能不正确")
                        
            except Exception as e:
                print(f"    ❌ 获取索引图容器信息失败: {e}")
        
        # 测试4：测试折叠/展开功能
        print("\n🔍 测试4：测试折叠/展开功能")
        
        if hasattr(app, '_toggle_legend'):
            print("    测试展开功能...")
            try:
                # 如果当前是折叠状态，展开它
                if app.legend_collapsed.get():
                    app._toggle_legend()
                    root.update()
                    time.sleep(1)
                    
                    new_state = app.legend_collapsed.get()
                    btn_text = app.legend_toggle_btn.cget('text')
                    status_text = app.legend_status_label.cget('text')
                    
                    print(f"    展开后状态: {'折叠' if new_state else '展开'}")
                    print(f"    展开后按钮文本: '{btn_text}'")
                    print(f"    展开后状态标签: '{status_text}'")
                    
                    if not new_state:
                        print("    ✅ 展开功能正常")
                    else:
                        print("    ❌ 展开功能异常")
                
                print("    测试折叠功能...")
                # 折叠索引图
                app._toggle_legend()
                root.update()
                time.sleep(1)
                
                final_state = app.legend_collapsed.get()
                final_btn_text = app.legend_toggle_btn.cget('text')
                final_status_text = app.legend_status_label.cget('text')
                
                print(f"    折叠后状态: {'折叠' if final_state else '展开'}")
                print(f"    折叠后按钮文本: '{final_btn_text}'")
                print(f"    折叠后状态标签: '{final_status_text}'")
                
                if final_state:
                    print("    ✅ 折叠功能正常")
                else:
                    print("    ❌ 折叠功能异常")
                    
            except Exception as e:
                print(f"    ❌ 测试折叠/展开功能失败: {e}")
                import traceback
                traceback.print_exc()
        else:
            print("    ❌ _toggle_legend 方法不存在")
        
        # 测试5：检查索引图内容是否正常
        print("\n🔍 测试5：检查索引图内容")
        
        # 展开索引图以检查内容
        if hasattr(app, 'legend_collapsed') and app.legend_collapsed.get():
            app._toggle_legend()
            root.update()
            time.sleep(1)
        
        # 检查索引图组件
        legend_components = [
            'legend_frame',
            'legend_fig',
            'legend_ax',
            'legend_canvas',
            'legend_font'
        ]
        
        for component in legend_components:
            if hasattr(app, component):
                value = getattr(app, component)
                print(f"    ✅ {component}: {type(value)}")
            else:
                print(f"    ❌ {component}: 不存在")
        
        # 测试6：检查布局对下方控制区域的影响
        print("\n🔍 测试6：检查对下方控制区域的影响")
        
        control_areas = [
            ('zoom_frame', '图像控制区'),
            ('color_frame', '配色系统区')
        ]
        
        for attr_name, area_name in control_areas:
            if hasattr(app, attr_name):
                frame = getattr(app, attr_name)
                try:
                    height = frame.winfo_height()
                    print(f"    {area_name}高度: {height}px")
                    
                    if height < 100:
                        print(f"      ⚠️ {area_name}高度过小，可能被压缩")
                    else:
                        print(f"      ✅ {area_name}高度正常")
                        
                except Exception as e:
                    print(f"    ❌ 获取{area_name}信息失败: {e}")
        
        # 等待用户观察
        print("\n⏳ 等待5秒让用户观察可折叠索引图...")
        time.sleep(5)
        
        # 关闭测试窗口
        root.destroy()
        
        print(f"\n🎉 可折叠索引图测试完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_collapsible_legend()
    if success:
        print("\n🎊 可折叠索引图测试成功！")
    else:
        print("\n⚠️ 可折叠索引图测试发现问题")
