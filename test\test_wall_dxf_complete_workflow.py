#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整测试wall.dxf文件的工作流
模拟从文件处理开始到分组完成的完整流程
测试分组信息、颜色显示和弧线显示
"""

import os
import sys
import time
import math

def load_and_analyze_wall_dxf():
    """加载并分析wall.dxf文件"""
    print("📁 步骤1：选择文件夹并加载文件")
    print("="*60)
    
    try:
        dxf_file_path = r"C:\A-BCXM\cad文件\cs-wall\wall.dxf"
        
        if not os.path.exists(dxf_file_path):
            print(f"❌ 文件不存在: {dxf_file_path}")
            return None
        
        print(f"✅ 选择文件夹: {os.path.dirname(dxf_file_path)}")
        print(f"✅ 目标文件: wall.dxf")
        print(f"📊 文件大小: {os.path.getsize(dxf_file_path)} 字节")
        
        # 读取DXF文件
        import ezdxf
        doc = ezdxf.readfile(dxf_file_path)
        msp = doc.modelspace()
        
        entities = list(msp)
        print(f"✅ 文件读取成功，共 {len(entities)} 个实体")
        
        return entities
        
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return None

def process_entities(entities):
    """步骤2：开始处理 - 转换实体格式"""
    print("\n🔄 步骤2：开始处理")
    print("="*60)
    
    try:
        print("🔍 分析实体类型和属性...")
        
        # 统计实体类型
        entity_types = {}
        layer_stats = {}
        converted_entities = []
        
        for i, entity in enumerate(entities):
            entity_type = entity.dxftype()
            layer = entity.dxf.layer
            
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            layer_stats[layer] = layer_stats.get(layer, 0) + 1
            
            # 转换为内部格式
            converted_entity = {
                'id': i + 1,
                'type': entity_type,
                'layer': layer,
                'label': None,
                'auto_labeled': False
            }
            
            # 添加几何信息
            try:
                if entity_type == 'LINE':
                    start = entity.dxf.start
                    end = entity.dxf.end
                    converted_entity['points'] = [(start.x, start.y), (end.x, end.y)]
                    converted_entity['start_point'] = (start.x, start.y)
                    converted_entity['end_point'] = (end.x, end.y)
                    
                elif entity_type == 'ELLIPSE':
                    center = entity.dxf.center
                    major_axis = entity.dxf.major_axis
                    ratio = entity.dxf.ratio
                    start_param = entity.dxf.start_param
                    end_param = entity.dxf.end_param
                    
                    converted_entity['points'] = [(center.x, center.y)]
                    converted_entity['center'] = (center.x, center.y)
                    converted_entity['major_axis'] = (major_axis.x, major_axis.y)
                    converted_entity['ratio'] = ratio
                    converted_entity['start_param'] = start_param
                    converted_entity['end_param'] = end_param
                    
                    # 计算等效半径（用于显示）
                    major_length = math.sqrt(major_axis.x**2 + major_axis.y**2)
                    converted_entity['radius'] = major_length
                    
                elif entity_type == 'ARC':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    start_angle = entity.dxf.start_angle
                    end_angle = entity.dxf.end_angle
                    
                    converted_entity['points'] = [(center.x, center.y)]
                    converted_entity['center'] = (center.x, center.y)
                    converted_entity['radius'] = radius
                    converted_entity['start_angle'] = start_angle
                    converted_entity['end_angle'] = end_angle
                    
                elif entity_type == 'CIRCLE':
                    center = entity.dxf.center
                    radius = entity.dxf.radius
                    
                    converted_entity['points'] = [(center.x, center.y)]
                    converted_entity['center'] = (center.x, center.y)
                    converted_entity['radius'] = radius
                    
                else:
                    # 其他类型实体的默认处理
                    converted_entity['points'] = [(0, 0)]
                    
            except Exception as e:
                print(f"⚠️ 实体{i+1}几何信息提取失败: {e}")
                converted_entity['points'] = [(0, 0)]
            
            converted_entities.append(converted_entity)
        
        print(f"📊 实体类型分布:")
        for entity_type, count in entity_types.items():
            print(f"  {entity_type}: {count} 个")
        
        print(f"📊 图层分布:")
        for layer, count in layer_stats.items():
            print(f"  {layer}: {count} 个")
        
        print(f"✅ 实体处理完成，转换了 {len(converted_entities)} 个实体")
        
        return converted_entities
        
    except Exception as e:
        print(f"❌ 实体处理失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def process_lines(entities):
    """步骤3：线条处理"""
    print("\n📏 步骤3：线条处理")
    print("="*60)
    
    try:
        print("🔍 分析线条连接关系...")
        
        # 统计线条
        lines = [e for e in entities if e['type'] == 'LINE']
        other_entities = [e for e in entities if e['type'] != 'LINE']
        
        print(f"📊 线条统计: {len(lines)} 条线段")
        print(f"📊 其他实体: {len(other_entities)} 个")
        
        # 按图层分组线条
        lines_by_layer = {}
        for line in lines:
            layer = line['layer']
            if layer not in lines_by_layer:
                lines_by_layer[layer] = []
            lines_by_layer[layer].append(line)
        
        print(f"📊 线条图层分布:")
        for layer, layer_lines in lines_by_layer.items():
            print(f"  {layer}: {len(layer_lines)} 条线段")
        
        # 简单的线条连接分析
        print("🔗 分析线条连接...")
        for layer, layer_lines in lines_by_layer.items():
            if len(layer_lines) > 1:
                connected_groups = find_connected_lines(layer_lines)
                print(f"  {layer}图层: {len(connected_groups)} 个连接组")
        
        print("✅ 线条处理完成")
        return entities
        
    except Exception as e:
        print(f"❌ 线条处理失败: {e}")
        return entities

def find_connected_lines(lines):
    """查找连接的线条组"""
    try:
        groups = []
        used_lines = set()
        
        for i, line in enumerate(lines):
            if i in used_lines:
                continue
            
            # 开始一个新组
            group = [line]
            used_lines.add(i)
            
            # 查找连接的线条
            changed = True
            while changed:
                changed = False
                for j, other_line in enumerate(lines):
                    if j in used_lines:
                        continue
                    
                    # 检查是否与组中任何线条连接
                    for group_line in group:
                        if lines_connected(group_line, other_line):
                            group.append(other_line)
                            used_lines.add(j)
                            changed = True
                            break
                    
                    if changed:
                        break
            
            groups.append(group)
        
        return groups
        
    except Exception as e:
        print(f"⚠️ 连接分析失败: {e}")
        return [lines]

def lines_connected(line1, line2, tolerance=1.0):
    """检查两条线是否连接"""
    try:
        start1 = line1['start_point']
        end1 = line1['end_point']
        start2 = line2['start_point']
        end2 = line2['end_point']
        
        # 检查端点是否重合
        def distance(p1, p2):
            return math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        
        return (distance(start1, start2) < tolerance or
                distance(start1, end2) < tolerance or
                distance(end1, start2) < tolerance or
                distance(end1, end2) < tolerance)
        
    except Exception as e:
        return False

def identify_groups(entities):
    """步骤4：识别分组"""
    print("\n🔍 步骤4：识别分组")
    print("="*60)
    
    try:
        print("🎯 基于图层和几何关系进行分组...")
        
        # 按图层分组
        groups_by_layer = {}
        for entity in entities:
            layer = entity['layer']
            if layer not in groups_by_layer:
                groups_by_layer[layer] = []
            groups_by_layer[layer].append(entity)
        
        # 进一步细分组（基于几何关系）
        final_groups = []
        group_info = []
        
        for layer, layer_entities in groups_by_layer.items():
            print(f"\n📊 处理图层: {layer} ({len(layer_entities)} 个实体)")
            
            # 按实体类型分组
            entities_by_type = {}
            for entity in layer_entities:
                entity_type = entity['type']
                if entity_type not in entities_by_type:
                    entities_by_type[entity_type] = []
                entities_by_type[entity_type].append(entity)
            
            # 对于线条，进行连接分析
            if 'LINE' in entities_by_type:
                lines = entities_by_type['LINE']
                connected_groups = find_connected_lines(lines)
                
                for i, connected_group in enumerate(connected_groups):
                    group_index = len(final_groups)
                    final_groups.append(connected_group)
                    
                    info = {
                        'group_index': group_index,
                        'layer': layer,
                        'type': 'LINE_GROUP',
                        'entity_count': len(connected_group),
                        'description': f'{layer}图层线条组{i+1}',
                        'status': 'unlabeled',
                        'label': None
                    }
                    group_info.append(info)
                    
                    print(f"  组{group_index + 1}: {info['description']} - {len(connected_group)} 条线段")
            
            # 处理其他类型实体
            for entity_type, type_entities in entities_by_type.items():
                if entity_type != 'LINE' and len(type_entities) > 0:
                    group_index = len(final_groups)
                    final_groups.append(type_entities)
                    
                    info = {
                        'group_index': group_index,
                        'layer': layer,
                        'type': entity_type,
                        'entity_count': len(type_entities),
                        'description': f'{layer}图层{entity_type}组',
                        'status': 'unlabeled',
                        'label': None
                    }
                    group_info.append(info)
                    
                    print(f"  组{group_index + 1}: {info['description']} - {len(type_entities)} 个{entity_type}")
        
        print(f"\n✅ 分组识别完成，共识别出 {len(final_groups)} 个组")
        
        return final_groups, group_info
        
    except Exception as e:
        print(f"❌ 分组识别失败: {e}")
        import traceback
        traceback.print_exc()
        return [], []

def test_color_display(groups, group_info):
    """测试分组完成后的颜色显示"""
    print("\n🎨 测试分组完成后的颜色显示")
    print("="*60)
    
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        print("✅ 可视化器初始化成功")
        
        # 测试每个组的颜色显示
        for i, (group, info) in enumerate(zip(groups, group_info)):
            print(f"\n🔍 测试组{i + 1}: {info['description']}")
            print(f"  📊 实体数量: {info['entity_count']}")
            print(f"  📊 实体类型: {info['type']}")
            print(f"  📊 图层: {info['layer']}")
            
            # 测试前3个实体的颜色
            test_entities = group[:3] if len(group) > 3 else group
            
            for j, entity in enumerate(test_entities):
                print(f"    实体{j + 1} ({entity['type']}, {entity['layer']}):")
                
                try:
                    # 测试作为当前组时的颜色
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=[],
                        current_group_entities=group,
                        processor=None
                    )
                    
                    print(f"      颜色: {color}")
                    print(f"      透明度: {alpha}")
                    print(f"      线宽: {linewidth}")
                    print(f"      状态: {status}")
                    
                    # 检查颜色是否正确
                    if color == '#8B4513':
                        print(f"      ⚠️ 发现问题：使用了墙体颜色")
                        
                        # 详细诊断
                        backup_color = visualizer._get_entity_color_by_type_and_layer(entity)
                        print(f"      备用颜色: {backup_color}")
                        
                        layer_name = str(entity.get('layer', '')).lower()
                        if 'wall' in layer_name:
                            print(f"      原因: 图层名包含'wall'")
                            
                    elif color == '#FF0000':
                        print(f"      ✅ 正确：红色高亮")
                    else:
                        print(f"      ℹ️ 其他颜色: {color}")
                    
                    # 特别检查弧线和椭圆
                    if entity['type'] in ['ARC', 'ELLIPSE', 'CIRCLE']:
                        print(f"      🌀 弧形实体检查:")
                        if 'center' in entity:
                            print(f"        中心: {entity['center']}")
                        if 'radius' in entity:
                            print(f"        半径: {entity['radius']}")
                        if 'start_angle' in entity and 'end_angle' in entity:
                            print(f"        角度: {entity['start_angle']}° - {entity['end_angle']}°")
                        print(f"        ✅ 弧形实体参数完整")
                        
                except Exception as e:
                    print(f"      ❌ 颜色测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_labeling(groups, group_info):
    """步骤5：选择类别（家具）- 测试手动标注"""
    print("\n🏷️ 步骤5：选择类别（家具）")
    print("="*60)
    
    try:
        from cad_visualizer import CADVisualizer
        visualizer = CADVisualizer()
        
        print("🎯 模拟手动标注流程...")
        
        # 选择第一个组进行标注
        if len(groups) > 0:
            target_group = groups[0]
            target_info = group_info[0]
            
            print(f"📌 选择组1进行标注: {target_info['description']}")
            print(f"  实体数量: {len(target_group)}")
            
            # 标注前的颜色状态
            print(f"\n🎨 标注前颜色状态:")
            test_entities = target_group[:3] if len(target_group) > 3 else target_group
            
            for j, entity in enumerate(test_entities):
                try:
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=[],
                        current_group_entities=target_group,
                        processor=None
                    )
                    
                    print(f"    实体{j + 1}: 颜色={color}, 状态={status}")
                    
                except Exception as e:
                    print(f"    实体{j + 1}: 颜色测试失败 - {e}")
            
            # 执行手动标注
            print(f"\n🏷️ 执行手动标注为'furniture'...")
            
            # 更新实体标签
            for entity in target_group:
                entity['label'] = 'furniture'
                entity['auto_labeled'] = False
            
            # 更新组信息
            target_info['status'] = 'labeled'
            target_info['label'] = 'furniture'
            
            print(f"✅ 标注完成")
            
            # 标注后的颜色状态
            print(f"\n🎨 标注后颜色状态:")
            
            for j, entity in enumerate(test_entities):
                try:
                    color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                        entity,
                        labeled_entities=target_group,  # 加入已标注列表
                        current_group_entities=target_group,  # 仍然是当前组
                        processor=None
                    )
                    
                    print(f"    实体{j + 1}:")
                    print(f"      标签: {entity.get('label', 'None')}")
                    print(f"      颜色: {color}")
                    print(f"      状态: {status}")
                    
                    # 验证标注结果
                    if entity.get('label') == 'furniture':
                        print(f"      ✅ 标签更新正确")
                    else:
                        print(f"      ❌ 标签更新失败")
                    
                    # 验证颜色变化
                    if status == 'current' and color == '#FF0000':
                        print(f"      ✅ 当前组高亮正确")
                    elif status == 'labeled' and color != '#808080':
                        print(f"      ✅ 标注后分类颜色正确")
                    else:
                        print(f"      ⚠️ 颜色状态需要检查")
                    
                    # 特别检查弧形实体的标注
                    if entity['type'] in ['ARC', 'ELLIPSE', 'CIRCLE']:
                        print(f"      🌀 弧形实体标注检查:")
                        if entity.get('label') == 'furniture':
                            print(f"        ✅ 弧形实体标签更新正确")
                        else:
                            print(f"        ❌ 弧形实体标签更新失败")
                        
                except Exception as e:
                    print(f"    实体{j + 1}: 标注后颜色测试失败 - {e}")
            
            return True
        else:
            print("❌ 没有可用的组进行标注测试")
            return False
        
    except Exception as e:
        print(f"❌ 手动标注测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 启动wall.dxf完整工作流测试")
    print("="*80)
    print("模拟操作步骤：选择文件夹 → 开始处理 → 线条处理 → 识别分组 → 选择类别（家具）")
    print("="*80)
    
    try:
        # 步骤1：选择文件夹并加载文件
        entities = load_and_analyze_wall_dxf()
        if not entities:
            return False
        
        # 步骤2：开始处理
        processed_entities = process_entities(entities)
        if not processed_entities:
            return False
        
        # 步骤3：线条处理
        line_processed_entities = process_lines(processed_entities)
        
        # 步骤4：识别分组
        groups, group_info = identify_groups(line_processed_entities)
        if not groups:
            return False
        
        # 测试分组完成后的颜色显示
        color_test_result = test_color_display(groups, group_info)
        
        # 步骤5：选择类别（家具）
        labeling_test_result = test_manual_labeling(groups, group_info)
        
        # 输出测试结果
        print("\n" + "="*80)
        print("📊 完整工作流测试结果")
        print("="*80)
        
        print(f"✅ 文件加载: 成功 ({len(entities)} 个实体)")
        print(f"✅ 实体处理: 成功 ({len(processed_entities)} 个实体)")
        print(f"✅ 线条处理: 成功")
        print(f"✅ 分组识别: 成功 ({len(groups)} 个组)")
        print(f"{'✅' if color_test_result else '❌'} 颜色显示测试: {'成功' if color_test_result else '失败'}")
        print(f"{'✅' if labeling_test_result else '❌'} 手动标注测试: {'成功' if labeling_test_result else '失败'}")
        
        overall_success = color_test_result and labeling_test_result
        
        if overall_success:
            print(f"\n🎉 所有测试通过！wall.dxf工作流完全正常！")
        else:
            print(f"\n⚠️ 部分测试失败，需要进一步检查")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
