#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 main_enhanced_with_v2_fill.py 的颜色显示修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_v2_color_system():
    """测试V2版本的颜色系统"""
    print("🔍 测试 main_enhanced_with_v2_fill.py 颜色系统")
    print("=" * 60)
    
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建V2应用实例（不启动GUI）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        app = EnhancedCADAppV2(root)
        
        # 初始化配色系统
        app._init_color_system_v2()
        
        print(f"✅ V2配色系统初始化完成")
        print(f"   配色方案包含: {len(app.current_color_scheme)} 种颜色")
        
        # 验证关键颜色
        key_colors = ['wall', 'door_window', 'unlabeled', 'labeling']
        print(f"\n🎨 验证关键颜色:")
        
        for color_key in key_colors:
            color_value = app.current_color_scheme.get(color_key, 'N/A')
            print(f"  {color_key}: {color_value}")
            
            # 验证门窗颜色是否为天蓝色
            if color_key == 'door_window':
                if color_value == '#87CEEB':
                    print(f"    ✅ 门窗颜色正确（天蓝色）")
                else:
                    print(f"    ❌ 门窗颜色错误，期望: #87CEEB")
        
        # 验证图例颜色映射
        print(f"\n🏷️ 验证图例颜色映射:")
        if hasattr(app, 'legend_category_colors'):
            legend_colors = app.legend_category_colors
            
            door_colors = [
                ('door', legend_colors.get('door', 'N/A')),
                ('window', legend_colors.get('window', 'N/A')),
                ('door_window', legend_colors.get('door_window', 'N/A'))
            ]
            
            for name, color in door_colors:
                print(f"  {name}: {color}")
                if color == '#87CEEB':
                    print(f"    ✅ {name}颜色正确")
                else:
                    print(f"    ❌ {name}颜色错误，期望: #87CEEB")
        
        # 测试可视化器颜色设置
        print(f"\n🖼️ 测试可视化器颜色设置:")
        if hasattr(app, 'visualizer') and app.visualizer:
            if hasattr(app.visualizer, 'color_scheme'):
                visualizer_door_color = app.visualizer.color_scheme.get('door_window', 'N/A')
                print(f"  可视化器门窗颜色: {visualizer_door_color}")
                
                if visualizer_door_color == '#87CEEB':
                    print(f"    ✅ 可视化器门窗颜色正确")
                else:
                    print(f"    ❌ 可视化器门窗颜色错误")
            else:
                print(f"  ⚠️ 可视化器配色方案未设置")
        else:
            print(f"  ⚠️ 可视化器未初始化")
        
        # 测试颜色获取方法
        print(f"\n🔧 测试颜色获取方法:")
        
        # 测试实体
        test_entity = {
            'id': 1,
            'type': 'LINE',
            'layer': 'A-WINDOW',
            'label': 'door_window',
            'auto_labeled': True
        }
        
        try:
            if hasattr(app, '_get_group_display_color'):
                # 测试组显示颜色获取
                group_info = {
                    'status': 'auto_labeled',
                    'label': 'door_window',
                    'group_type': 'door_window'
                }
                
                display_color = app._get_group_display_color(0, group_info)
                print(f"  组显示颜色: {display_color}")
                
                if display_color == '#87CEEB':
                    print(f"    ✅ 组显示颜色正确")
                else:
                    print(f"    ❌ 组显示颜色错误，期望: #87CEEB")
        
        except Exception as e:
            print(f"  ⚠️ 颜色获取方法测试失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ V2颜色系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_color_consistency():
    """测试颜色一致性"""
    print(f"\n🔄 测试颜色一致性:")
    print("-" * 40)
    
    try:
        # 检查文件中的颜色定义
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 统计门窗颜色的使用
        door_window_colors = {
            '#87CEEB': content.count('#87CEEB'),
            '#FFD700': content.count('#FFD700'),
            '#FF0000': content.count('#FF0000')
        }
        
        print(f"  门窗颜色使用统计:")
        for color, count in door_window_colors.items():
            color_name = {
                '#87CEEB': '天蓝色',
                '#FFD700': '金色',
                '#FF0000': '红色'
            }.get(color, '未知')
            
            print(f"    {color} ({color_name}): {count} 次")
        
        # 检查是否还有不一致的颜色
        if door_window_colors['#FFD700'] > 2:  # UI高亮色除外
            print(f"    ⚠️ 仍有金色门窗颜色定义")
        else:
            print(f"    ✅ 门窗颜色基本统一")
        
        return True
        
    except Exception as e:
        print(f"❌ 颜色一致性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试 V2 版本颜色显示修复")
    
    success = True
    
    # 测试1：V2颜色系统
    if not test_v2_color_system():
        success = False
    
    # 测试2：颜色一致性
    if not test_color_consistency():
        success = False
    
    if success:
        print(f"\n🎉 V2版本颜色显示修复测试通过")
        print(f"💡 现在 main_enhanced_with_v2_fill.py 中的门窗颜色应该统一为天蓝色 #87CEEB")
    else:
        print(f"\n❌ 部分测试失败，需要进一步检查")
