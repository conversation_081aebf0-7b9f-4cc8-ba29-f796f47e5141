#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试组匹配统计功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_group_matching_stats():
    """测试组匹配统计功能"""
    
    print("🔍 测试组匹配统计功能")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建模拟的34个组数据
        all_groups = []
        groups_info = []
        all_entities = []
        
        print("📊 创建模拟的34个组数据:")
        
        # 组0-8：墙体组（有实体）
        for i in range(9):
            group_entities = []
            for j in range(5):
                entity = {
                    'type': 'LINE',
                    'layer': 'A-WALL',
                    'points': [[i*10+j, 0], [i*10+j+5, 0]],
                    'id': f'wall_{i}_{j}'
                }
                group_entities.append(entity)
                all_entities.append(entity)
            
            all_groups.append(group_entities)
            groups_info.append({'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False})
        
        # 组9：门窗组（有实体）
        group_entities = []
        for j in range(8):
            entity = {
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'points': [[90+j, 10], [90+j+3, 10]],
                'id': f'window_9_{j}'
            }
            group_entities.append(entity)
            all_entities.append(entity)
        
        all_groups.append(group_entities)
        groups_info.append({'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False})
        
        # 组10-24：空组（模拟真实情况中的空组）
        for i in range(10, 25):
            all_groups.append([])  # 空组
            groups_info.append({'status': 'unlabeled', 'label': '未标注', 'is_current_group': False})
        
        # 组25：标注中组（有实体）
        group_entities = []
        for j in range(4):
            entity = {
                'type': 'LINE',
                'layer': '',  # 空layer
                'points': [[250+j, 20], [250+j+2, 20]],
                'id': f'labeling_{j}'
            }
            group_entities.append(entity)
            all_entities.append(entity)
        
        all_groups.append(group_entities)
        groups_info.append({'status': 'labeling', 'label': '未标注', 'is_current_group': False})
        
        # 组26-33：空组
        for i in range(26, 34):
            all_groups.append([])  # 空组
            groups_info.append({'status': 'unlabeled', 'label': '未标注', 'is_current_group': False})
        
        print(f"✅ 创建了{len(all_groups)}个组，总计{len(all_entities)}个实体")
        print(f"  有实体的组: 0-9, 25 (共11个)")
        print(f"  空组: 10-24, 26-33 (共23个)")
        
        # 模拟配色方案和可视化器
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#87CEEB',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
                self.ax_overview = MockAxes()
        
        class MockAxes:
            def clear(self): pass
            def set_aspect(self, aspect): pass
            def grid(self, *args, **kwargs): pass
            def plot(self, *args, **kwargs): pass
            def set_title(self, title, **kwargs): pass
        
        visualizer = MockVisualizer()
        
        # 模拟处理器
        class MockProcessor:
            def __init__(self):
                self.all_groups = all_groups
                self.groups_info = groups_info
        
        processor = MockProcessor()
        
        print("\n🧪 运行批量概览并查看统计:")
        print("=" * 40)
        
        # 运行批量概览
        result = fix._batch_overview(
            visualizer, all_entities, [], [], processor, None
        )
        
        print(f"\n📋 预期结果:")
        print(f"  应该匹配的组: 0-9, 25 (共11个)")
        print(f"  应该未匹配的组: 10-24, 26-33 (共23个)")
        print(f"  所有实体都应该被匹配")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_real_world_scenario():
    """分析真实世界场景"""
    
    print("\n🌍 真实世界场景分析")
    print("=" * 30)
    
    print("🎯 您的实际情况:")
    print("  - 总共34个组")
    print("  - 只有3个组被处理：组0(A-WALL), 组9(A-WINDOW), 组25(标注中)")
    print("  - 其他31个组没有被处理")
    
    print("\n🔍 可能的原因:")
    print("  1. **空组** - 其他31个组可能为空")
    print("  2. **实体重复** - 所有实体都集中在这3个组中")
    print("  3. **数据损坏** - 其他组的数据可能损坏")
    print("  4. **匹配失败** - ID或属性匹配失败")
    
    print("\n💡 调试策略:")
    print("  1. 查看组匹配统计输出")
    print("  2. 确认每个组的实体数量")
    print("  3. 检查实体ID的一致性")
    print("  4. 验证组信息的完整性")

def provide_next_steps():
    """提供下一步操作"""
    
    print("\n🚀 下一步操作")
    print("=" * 20)
    
    print("📝 立即行动:")
    print("  1. **运行实际测试** - 在真实环境中查看组匹配统计")
    print("  2. **检查空组** - 确认其他31个组是否为空")
    print("  3. **验证数据** - 检查组信息和实体数据的一致性")
    print("  4. **分析原因** - 根据统计结果确定问题根源")
    
    print("\n🔧 可能的修复:")
    print("  - 如果是空组：正常现象，无需修复")
    print("  - 如果是匹配失败：增强匹配逻辑")
    print("  - 如果是数据损坏：修复数据结构")
    print("  - 如果是ID变化：使用更稳定的匹配方式")

if __name__ == "__main__":
    print("🔍 组匹配统计测试程序")
    print("=" * 50)
    
    # 测试组匹配统计
    test_result = test_group_matching_stats()
    
    # 分析真实世界场景
    analyze_real_world_scenario()
    
    # 提供下一步操作
    provide_next_steps()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结:")
    
    if test_result:
        print("✅ 组匹配统计功能正常")
        print("✅ 可以正确识别匹配和未匹配的组")
        print("✅ 统计信息输出完整")
        print("\n🔍 现在可以在实际环境中:")
        print("  - 查看详细的组匹配统计")
        print("  - 确认哪些组有实体，哪些组为空")
        print("  - 分析为什么只有3个组被处理")
    else:
        print("❌ 组匹配统计测试失败")
        print("❌ 需要检查统计逻辑")
    
    print("\n💡 关键问题:")
    print("  您的34个组中，可能只有3个组真正包含实体！")
    print("  其他31个组可能是空的或数据有问题。")
    print("  通过新的统计功能，我们很快就能确认这一点。")
