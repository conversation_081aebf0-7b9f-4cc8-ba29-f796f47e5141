#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试实体匹配修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_entity_matching_fix():
    """测试实体匹配修复"""
    
    print("🔍 测试实体匹配修复")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        wall_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'wall_1'
        }
        
        window_entity = {
            'type': 'LINE', 
            'layer': 'A-WINDOW',
            'points': [[5, 0], [7, 0]],
            'id': 'window_1'
        }
        
        # 模拟真实的配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
        
        visualizer = MockVisualizer()
        
        # 模拟组信息（与您提供的真实数据一致）
        all_groups = [
            [wall_entity],      # 组0：墙体
            [window_entity]     # 组1：门窗
        ]
        
        groups_info = [
            {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},
            {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False}
        ]
        
        print("🧪 测试实体匹配和颜色获取:")
        
        # 测试墙体实体
        print(f"\n  测试1: 墙体实体")
        print(f"  " + "-" * 30)
        
        # 重置调试标记
        if hasattr(fix, '_batch_color_debug_shown'):
            delattr(fix, '_batch_color_debug_shown')
        
        wall_color = fix._get_entity_color_for_batch_enhanced(
            wall_entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        
        print(f"  墙体实体颜色: {wall_color}")
        print(f"  期望颜色: #8B4513 (棕色)")
        
        # 测试门窗实体
        print(f"\n  测试2: 门窗实体")
        print(f"  " + "-" * 30)
        
        window_color = fix._get_entity_color_for_batch_enhanced(
            window_entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        
        print(f"  门窗实体颜色: {window_color}")
        print(f"  期望颜色: #FFD700 (金色)")
        
        # 验证结果
        wall_correct = wall_color == '#8B4513'
        window_correct = window_color == '#FFD700'
        
        print(f"\n📊 测试结果:")
        print(f"  墙体颜色: {'✅ 正确' if wall_correct else '❌ 错误'}")
        print(f"  门窗颜色: {'✅ 正确' if window_correct else '❌ 错误'}")
        
        return wall_correct and window_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_entity_matching_scenarios():
    """测试不同的实体匹配场景"""
    
    print("\n🎯 测试实体匹配场景")
    print("=" * 35)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        test_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'test_wall'
        }
        
        # 创建组中的实体（模拟不同的匹配情况）
        group_entity_same_id = test_entity  # 相同对象
        group_entity_same_attrs = {         # 不同对象，相同属性
            'type': 'LINE',
            'layer': 'A-WALL', 
            'points': [[0, 0], [10, 0]],
            'id': 'different_id'
        }
        
        color_scheme = {'wall': '#8B4513', 'unlabeled': '#D3D3D3'}
        
        print("📋 测试场景:")
        
        # 场景1：ID匹配成功
        print(f"\n  场景1: ID匹配")
        all_groups_1 = [[group_entity_same_id]]
        groups_info_1 = [{'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}]
        
        color_1 = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, None, [], all_groups_1, groups_info_1, None
        )
        print(f"    结果颜色: {color_1}")
        
        # 场景2：属性匹配成功
        print(f"\n  场景2: 属性匹配")
        all_groups_2 = [[group_entity_same_attrs]]
        groups_info_2 = [{'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}]
        
        color_2 = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, None, [], all_groups_2, groups_info_2, None
        )
        print(f"    结果颜色: {color_2}")
        
        # 场景3：匹配失败
        print(f"\n  场景3: 匹配失败")
        different_entity = {
            'type': 'CIRCLE',
            'layer': 'A-FURNITURE',
            'points': [[20, 20]],
            'id': 'furniture_1'
        }
        all_groups_3 = [[different_entity]]
        groups_info_3 = [{'status': 'auto_labeled', 'label': 'furniture', 'is_current_group': False}]
        
        color_3 = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, None, [], all_groups_3, groups_info_3, None
        )
        print(f"    结果颜色: {color_3}")
        
        return True
        
    except Exception as e:
        print(f"❌ 场景测试失败: {e}")
        return False

def provide_fix_summary():
    """提供修复总结"""
    
    print("\n📋 实体匹配修复总结")
    print("=" * 30)
    
    print("🔧 修复的问题:")
    print("  ❌ 原问题：实体ID匹配失败，所有实体显示灰色")
    print("  ✅ 修复：增加多种匹配方式，提高匹配成功率")
    print()
    print("🎯 修复内容:")
    print("  1. **增强实体匹配逻辑**")
    print("     - 方式1：ID匹配 (id(entity))")
    print("     - 方式2：属性匹配 (layer + type)")
    print("     - 提高匹配成功率")
    print()
    print("  2. **详细的调试输出**")
    print("     - 输出实体匹配信息")
    print("     - 显示匹配成功的组")
    print("     - 确认使用的组信息")
    print()
    print("  3. **防御性编程**")
    print("     - 处理匹配失败的情况")
    print("     - 提供清晰的错误信息")
    print("     - 确保不会崩溃")
    print()
    print("🚀 预期效果:")
    print("  - auto_labeled + wall → 棕色 (#8B4513)")
    print("  - auto_labeled + door_window → 金色 (#FFD700)")
    print("  - labeling → 红色 (#FF0000)")
    print("  - unlabeled → 灰色 (#D3D3D3)")
    print("  - 实体匹配成功率大幅提升")

if __name__ == "__main__":
    print("🔍 实体匹配修复测试程序")
    print("=" * 50)
    
    # 测试实体匹配修复
    matching_result = test_entity_matching_fix()
    
    # 测试不同匹配场景
    scenario_result = test_entity_matching_scenarios()
    
    # 提供修复总结
    provide_fix_summary()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结:")
    
    if matching_result and scenario_result:
        print("✅ 实体匹配修复验证通过")
        print("✅ 多种匹配场景测试正常")
        print("✅ 详细调试输出已添加")
        print("\n🚀 现在批量概览应该显示正确颜色！")
        print("  - 实体匹配成功率大幅提升")
        print("  - 详细的调试信息帮助诊断")
        print("  - 墙体和门窗将显示正确颜色")
    else:
        print("❌ 部分测试失败")
        if not matching_result:
            print("❌ 实体匹配修复测试失败")
        if not scenario_result:
            print("❌ 匹配场景测试失败")
