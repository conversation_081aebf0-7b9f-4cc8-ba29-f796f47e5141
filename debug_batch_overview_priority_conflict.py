#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试批量概览颜色优先级冲突
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_color_method_priority():
    """调试颜色方法优先级冲突"""
    
    print("🔍 调试批量概览颜色优先级冲突")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        test_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'test_entity'
        }
        
        # 模拟真实的配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
            
            def _get_entity_display_info_enhanced(self, entity, **kwargs):
                """模拟可视化器的增强颜色获取方法"""
                print(f"    📞 调用可视化器的_get_entity_display_info_enhanced")
                print(f"      实体: {entity.get('layer', 'unknown')}")
                print(f"      参数: {list(kwargs.keys())}")
                
                # 模拟返回正确的颜色
                return '#8B4513', 1.0, 1.0, 'auto_labeled'
        
        visualizer = MockVisualizer()
        
        # 测试场景1：有组信息，实体在组中
        print("\n🧪 测试场景1：有组信息，实体在组中")
        all_groups = [[test_entity]]  # 实体在第1组
        groups_info = [{'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}]
        
        print("  调用_get_entity_color_for_batch_enhanced...")
        color1 = fix._get_entity_color_for_batch_enhanced(
            test_entity, color_scheme, visualizer, [], all_groups, groups_info, None
        )
        print(f"  返回颜色: {color1}")
        print(f"  期望颜色: #8B4513 (墙体颜色)")
        
        # 测试场景2：检查_get_color_from_group_info方法
        print("\n🧪 测试场景2：直接测试_get_color_from_group_info")
        group_info = {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}
        
        print("  调用_get_color_from_group_info...")
        color2 = fix._get_color_from_group_info(group_info, color_scheme)
        print(f"  返回颜色: {color2}")
        print(f"  期望颜色: #8B4513 (墙体颜色)")
        
        # 测试场景3：检查是否有其他方法被调用
        print("\n🧪 测试场景3：检查方法调用链")
        
        # 检查是否有可视化器的增强方法被调用
        if hasattr(visualizer, '_get_entity_display_info_enhanced'):
            print("  ✅ 可视化器有_get_entity_display_info_enhanced方法")
            try:
                enhanced_color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
                    test_entity,
                    labeled_entities=[],
                    current_group_entities=[],
                    all_groups=all_groups,
                    groups_info=groups_info,
                    processor=None
                )
                print(f"  增强方法返回颜色: {enhanced_color}")
            except Exception as e:
                print(f"  增强方法调用失败: {e}")
        else:
            print("  ❌ 可视化器没有_get_entity_display_info_enhanced方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_method_implementation():
    """检查方法实现"""
    
    print("\n🔧 检查方法实现")
    print("=" * 30)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 检查关键方法是否存在
        methods_to_check = [
            '_get_entity_color_for_batch_enhanced',
            '_get_color_from_group_info',
            '_batch_overview'
        ]
        
        print("📋 检查关键方法:")
        for method_name in methods_to_check:
            if hasattr(fix, method_name):
                method = getattr(fix, method_name)
                print(f"  ✅ {method_name} - 存在")
                print(f"    类型: {type(method)}")
                if hasattr(method, '__doc__'):
                    doc = method.__doc__
                    if doc:
                        first_line = doc.split('\n')[0].strip()
                        print(f"    描述: {first_line}")
            else:
                print(f"  ❌ {method_name} - 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def analyze_priority_conflict():
    """分析优先级冲突"""
    
    print("\n🎯 分析优先级冲突")
    print("=" * 25)
    
    print("🔍 可能的冲突原因:")
    print()
    print("1. **方法调用顺序问题**")
    print("   - 批量概览调用了错误的颜色获取方法")
    print("   - 可视化器的增强方法被跳过")
    print("   - _get_color_from_group_info方法有bug")
    print()
    print("2. **配色方案问题**")
    print("   - color_scheme中的颜色键名不匹配")
    print("   - unlabeled颜色被错误使用")
    print("   - 标签到颜色的映射有问题")
    print()
    print("3. **组信息格式问题**")
    print("   - groups_info中的status或label格式不正确")
    print("   - 'auto_labeled'状态没有被正确处理")
    print("   - 标签'wall'没有映射到正确颜色")
    print()
    print("4. **实体匹配问题**")
    print("   - 实体ID匹配失败")
    print("   - 实体不在预期的组中")
    print("   - 组索引越界或不匹配")
    print()
    print("💡 解决方案建议:")
    print()
    print("1. **增强调试输出**")
    print("   - 在_get_color_from_group_info中添加详细日志")
    print("   - 输出每个步骤的判断结果")
    print("   - 显示实际的组信息内容")
    print()
    print("2. **强制颜色映射**")
    print("   - 确保'auto_labeled' + 'wall'组合返回#8B4513")
    print("   - 添加防御性编程，处理边界情况")
    print("   - 提供明确的回退机制")
    print()
    print("3. **验证组信息完整性**")
    print("   - 检查groups_info的数据结构")
    print("   - 验证status和label字段的值")
    print("   - 确保组索引和实体匹配正确")

def provide_immediate_fix():
    """提供立即修复方案"""
    
    print("\n🚀 立即修复方案")
    print("=" * 20)
    
    print("🔧 建议的修复步骤:")
    print()
    print("1. **在_get_color_from_group_info中添加强制调试**")
    print("   - 输出接收到的group_info")
    print("   - 输出每个优先级判断的结果")
    print("   - 输出最终返回的颜色")
    print()
    print("2. **添加强制颜色映射**")
    print("   - 对于'auto_labeled' + 'wall'强制返回#8B4513")
    print("   - 对于'auto_labeled' + 'door_window'强制返回#FFD700")
    print("   - 确保不会回退到灰色")
    print()
    print("3. **验证实际调用**")
    print("   - 在批量概览中添加实时颜色输出")
    print("   - 确认_get_color_from_group_info被正确调用")
    print("   - 验证返回的颜色确实被使用")

if __name__ == "__main__":
    print("🔍 批量概览颜色优先级冲突调试程序")
    print("=" * 50)
    
    # 调试颜色方法优先级
    priority_result = debug_color_method_priority()
    
    # 检查方法实现
    method_result = check_method_implementation()
    
    # 分析优先级冲突
    analyze_priority_conflict()
    
    # 提供立即修复方案
    provide_immediate_fix()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结:")
    
    if priority_result and method_result:
        print("✅ 方法调用和实现检查通过")
        print("⚠️ 需要进一步调试实际的颜色获取流程")
        print("\n💡 下一步:")
        print("  1. 在_get_color_from_group_info中添加详细调试")
        print("  2. 强制处理'auto_labeled' + 'wall'组合")
        print("  3. 验证实际的批量概览调用")
    else:
        print("❌ 部分检查失败")
        if not priority_result:
            print("❌ 颜色方法优先级调试失败")
        if not method_result:
            print("❌ 方法实现检查失败")
