#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
诊断概览图颜色显示问题的详细工具
检查实际使用场景中的颜色显示情况
"""

import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_color_issue():
    """诊断概览图颜色显示问题"""
    print("🔍 开始诊断概览图颜色显示问题...")
    
    try:
        # 导入主程序
        import tkinter as tk
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("✅ 模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("颜色显示诊断工具")
        root.geometry("800x600")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        print("✅ 应用实例创建成功")
        
        # 等待界面完全加载
        root.update()
        
        # 诊断1：检查当前配色方案
        print("\n🔍 诊断1：检查当前配色方案")
        
        if hasattr(app, 'current_color_scheme') and app.current_color_scheme:
            print(f"  当前配色方案包含 {len(app.current_color_scheme)} 种颜色:")
            for key, color in app.current_color_scheme.items():
                print(f"    {key}: {color}")
        else:
            print(f"  ❌ 当前配色方案未设置")
        
        if hasattr(app.visualizer, 'color_scheme') and app.visualizer.color_scheme:
            print(f"  可视化器配色方案包含 {len(app.visualizer.color_scheme)} 种颜色:")
            for key, color in app.visualizer.color_scheme.items():
                print(f"    {key}: {color}")
        else:
            print(f"  ❌ 可视化器配色方案未设置")
        
        # 诊断2：检查处理器数据
        print("\n🔍 诊断2：检查处理器数据")
        
        if hasattr(app, 'processor') and app.processor:
            if hasattr(app.processor, 'all_groups'):
                print(f"  处理器包含 {len(app.processor.all_groups)} 个组")
                
                # 检查组信息
                if hasattr(app.processor, 'groups_info'):
                    print(f"  组信息包含 {len(app.processor.groups_info)} 个条目:")
                    for i, group_info in enumerate(app.processor.groups_info):
                        status = group_info.get('status', '未知')
                        label = group_info.get('label', '未知')
                        print(f"    组 {i+1}: 状态={status}, 标签={label}")
                        
                        # 测试颜色获取
                        try:
                            color = app.visualizer._get_group_color(group_info)
                            print(f"      获取的颜色: {color}")
                        except Exception as e:
                            print(f"      颜色获取失败: {e}")
                else:
                    print(f"  ❌ 处理器没有组信息")
            else:
                print(f"  ❌ 处理器没有组数据")
        else:
            print(f"  ❌ 处理器未初始化")
        
        # 诊断3：检查概览图当前状态
        print("\n🔍 诊断3：检查概览图当前状态")
        
        if hasattr(app.visualizer, 'ax_overview'):
            lines = app.visualizer.ax_overview.get_lines()
            patches = app.visualizer.ax_overview.patches
            
            print(f"  概览图当前状态:")
            print(f"    线条数量: {len(lines)}")
            print(f"    图形数量: {len(patches)}")
            
            # 统计颜色使用
            colors_used = {}
            for i, line in enumerate(lines):
                color = str(line.get_color())
                colors_used[color] = colors_used.get(color, 0) + 1
                if i < 5:  # 只显示前5条线的详细信息
                    print(f"    线条 {i+1}: 颜色={color}")
            
            for i, patch in enumerate(patches):
                color = str(patch.get_edgecolor())
                colors_used[color] = colors_used.get(color, 0) + 1
                if i < 5:  # 只显示前5个图形的详细信息
                    print(f"    图形 {i+1}: 边框颜色={color}")
            
            print(f"  颜色使用统计:")
            for color, count in colors_used.items():
                print(f"    {color}: {count} 次")
            
            # 判断颜色多样性
            if len(colors_used) <= 1:
                print(f"  ⚠️ 只使用了 {len(colors_used)} 种颜色，可能存在颜色显示问题")
            else:
                print(f"  ✅ 使用了 {len(colors_used)} 种颜色，颜色显示正常")
        else:
            print(f"  ❌ 概览图轴未初始化")
        
        # 诊断4：测试手动刷新概览图
        print("\n🔍 诊断4：测试手动刷新概览图")
        
        try:
            # 尝试手动刷新概览图
            if hasattr(app, 'visualize_overview'):
                print(f"  尝试手动刷新概览图...")
                app.visualize_overview()
                
                # 再次检查颜色
                lines_after = app.visualizer.ax_overview.get_lines()
                patches_after = app.visualizer.ax_overview.patches
                
                colors_after = {}
                for line in lines_after:
                    color = str(line.get_color())
                    colors_after[color] = colors_after.get(color, 0) + 1
                
                for patch in patches_after:
                    color = str(patch.get_edgecolor())
                    colors_after[color] = colors_after.get(color, 0) + 1
                
                print(f"  刷新后颜色使用统计:")
                for color, count in colors_after.items():
                    print(f"    {color}: {count} 次")
                
                if len(colors_after) <= 1:
                    print(f"  ⚠️ 刷新后仍只使用了 {len(colors_after)} 种颜色")
                else:
                    print(f"  ✅ 刷新后使用了 {len(colors_after)} 种颜色")
            else:
                print(f"  ❌ 概览图刷新方法不存在")
                
        except Exception as e:
            print(f"  ❌ 手动刷新概览图失败: {e}")
        
        # 诊断5：检查视图切换状态
        print("\n🔍 诊断5：检查视图切换状态")
        
        if hasattr(app, 'current_view_mode'):
            print(f"  当前视图模式: {app.current_view_mode}")
            
            # 测试视图切换
            try:
                print(f"  测试视图切换...")
                original_mode = app.current_view_mode
                
                # 切换视图
                app._toggle_view_mode()
                print(f"  切换后视图模式: {app.current_view_mode}")
                
                # 检查切换后的颜色
                lines_switched = app.visualizer.ax_overview.get_lines()
                colors_switched = {}
                for line in lines_switched:
                    color = str(line.get_color())
                    colors_switched[color] = colors_switched.get(color, 0) + 1
                
                print(f"  视图切换后颜色使用统计:")
                for color, count in colors_switched.items():
                    print(f"    {color}: {count} 次")
                
                # 切换回原来的视图
                if app.current_view_mode != original_mode:
                    app._toggle_view_mode()
                    print(f"  已切换回原视图模式: {app.current_view_mode}")
                
            except Exception as e:
                print(f"  ❌ 视图切换测试失败: {e}")
        else:
            print(f"  ❌ 视图模式信息不存在")
        
        # 显示诊断窗口
        print(f"\n🔍 显示诊断窗口...")
        print(f"请检查概览图的实际显示效果")
        print(f"窗口将在10秒后自动关闭...")
        
        # 让窗口显示10秒
        root.after(10000, root.quit)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = diagnose_color_issue()
    if success:
        print("\n🎊 诊断完成！")
        print("请根据诊断结果分析颜色显示问题")
    else:
        print("\n⚠️ 诊断过程中出现错误")
