#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重置视图按钮是否已正确添加到图像控制区域
"""

import tkinter as tk
import sys
import os

def test_reset_view_button():
    """测试重置视图按钮是否存在"""
    try:
        # 导入主程序
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("🔍 测试重置视图按钮...")
        
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        
        # 检查_create_zoom_buttons_area方法是否存在
        if hasattr(app, '_create_zoom_buttons_area'):
            print("✅ _create_zoom_buttons_area 方法存在")
            
            # 创建测试容器
            test_frame = tk.Frame(root)
            
            # 调用方法创建按钮区域
            app._create_zoom_buttons_area(test_frame)
            
            # 检查是否有重置视图按钮
            reset_button_found = False
            for child in test_frame.winfo_children():
                if isinstance(child, tk.Frame):
                    for button in child.winfo_children():
                        if isinstance(button, tk.Button):
                            button_text = button.cget('text')
                            if '重置' in button_text and '视图' in button_text:
                                reset_button_found = True
                                print(f"✅ 找到重置视图按钮: '{button_text}'")
                                
                                # 检查按钮命令
                                command = button.cget('command')
                                if command:
                                    print(f"✅ 按钮命令已绑定")
                                else:
                                    print(f"⚠️ 按钮命令未绑定")
                                break
            
            if not reset_button_found:
                print("❌ 未找到重置视图按钮")
                return False
            
            # 检查_reset_view方法是否存在
            if hasattr(app, '_reset_view'):
                print("✅ _reset_view 方法存在")
            else:
                print("❌ _reset_view 方法不存在")
                return False
                
            print("✅ 重置视图按钮测试通过")
            return True
            
        else:
            print("❌ _create_zoom_buttons_area 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            root.destroy()
        except:
            pass

def test_button_layout():
    """测试按钮布局是否正确"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        print("\n🔍 测试按钮布局...")
        
        root = tk.Tk()
        root.withdraw()
        
        app = EnhancedCADAppV2(root)
        test_frame = tk.Frame(root)
        
        # 创建按钮区域
        app._create_zoom_buttons_area(test_frame)
        
        # 统计按钮数量
        button_count = 0
        button_texts = []
        
        for child in test_frame.winfo_children():
            if isinstance(child, tk.Frame):
                for widget in child.winfo_children():
                    if isinstance(widget, tk.Button):
                        button_count += 1
                        button_texts.append(widget.cget('text'))
        
        print(f"✅ 找到 {button_count} 个按钮")
        for i, text in enumerate(button_texts, 1):
            print(f"  按钮 {i}: {text}")
        
        # 检查是否有三个主要按钮
        expected_buttons = ['缩放查看', '适应', '重置']
        found_buttons = []
        
        for expected in expected_buttons:
            for text in button_texts:
                if expected in text:
                    found_buttons.append(expected)
                    break
        
        if len(found_buttons) == 3:
            print("✅ 三个主要按钮都存在")
            return True
        else:
            print(f"❌ 缺少按钮，只找到: {found_buttons}")
            return False
            
    except Exception as e:
        print(f"❌ 布局测试失败: {e}")
        return False
    finally:
        try:
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    print("=" * 50)
    print("重置视图按钮测试")
    print("=" * 50)
    
    # 测试按钮是否存在
    test1_result = test_reset_view_button()
    
    # 测试按钮布局
    test2_result = test_button_layout()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"按钮存在测试: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"按钮布局测试: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！重置视图按钮已成功添加")
    else:
        print("⚠️ 部分测试失败，请检查实现")
    
    print("=" * 50)
