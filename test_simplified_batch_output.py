#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化的批量概览输出
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simplified_output():
    """测试简化的批量概览输出"""
    
    print("🔍 测试简化的批量概览输出")
    print("=" * 50)
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 创建测试实体
        wall_entity = {
            'type': 'LINE',
            'layer': 'A-WALL',
            'points': [[0, 0], [10, 0]],
            'id': 'wall_1'
        }
        
        window_entity = {
            'type': 'LINE', 
            'layer': 'A-WINDOW',
            'points': [[5, 0], [7, 0]],
            'id': 'window_1'
        }
        
        labeling_entity = {
            'type': 'LINE',
            'layer': 'A-FURNITURE',
            'points': [[15, 0], [20, 0]],
            'id': 'furniture_1'
        }
        
        unlabeled_entity = {
            'type': 'LINE',
            'layer': 'A-OTHER',
            'points': [[25, 0], [30, 0]],
            'id': 'other_1'
        }
        
        # 模拟真实的配色方案
        color_scheme = {
            'wall': '#8B4513',
            'door_window': '#FFD700',
            'current_group': '#FF0000',
            'unlabeled': '#D3D3D3',
            'other': '#808080'
        }
        
        # 模拟可视化器
        class MockVisualizer:
            def __init__(self):
                self.color_scheme = color_scheme
        
        visualizer = MockVisualizer()
        
        # 模拟组信息（与您提供的真实数据一致）
        all_groups = [
            [wall_entity],         # 组0：墙体 (auto_labeled)
            [window_entity],       # 组1：门窗 (auto_labeled)
            [labeling_entity],     # 组2：标注中 (labeling)
            [unlabeled_entity]     # 组3：未标注 (unlabeled)
        ]
        
        groups_info = [
            {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False},
            {'status': 'auto_labeled', 'label': 'door_window', 'is_current_group': False},
            {'status': 'labeling', 'label': '未标注', 'is_current_group': False},
            {'status': 'unlabeled', 'label': '未标注', 'is_current_group': False}
        ]
        
        print("🧪 测试简化输出格式:")
        print("=" * 30)
        
        test_entities = [wall_entity, window_entity, labeling_entity, unlabeled_entity]
        
        for i, entity in enumerate(test_entities):
            print(f"\n  实体{i+1}: {entity.get('layer', 'unknown')}")
            
            color = fix._get_entity_color_for_batch_enhanced(
                entity, color_scheme, visualizer, [], all_groups, groups_info, None
            )
            
            print(f"  最终颜色: {color}")
        
        print("\n" + "=" * 30)
        print("📋 预期的简化输出格式:")
        print("  组0: ✅ 优先级3-wall: #8B4513")
        print("  实体颜色: 0 -> #8B4513")
        print()
        print("  组1: ✅ 优先级3-door_window: #FFD700")
        print("  实体颜色: 1 -> #FFD700")
        print()
        print("  组2: ✅ 优先级2-标注中: #FF0000")
        print("  实体颜色: 2 -> #FF0000")
        print()
        print("  组3: ✅ 优先级4-未标注: #D3D3D3")
        print("  实体颜色: 3 -> #D3D3D3")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def demonstrate_output_comparison():
    """演示输出对比"""
    
    print("\n📊 输出格式对比")
    print("=" * 25)
    
    print("🔧 修改前的冗余输出:")
    print("  🎨 [实时调试] _get_color_from_group_info:")
    print("    输入组信息: {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}")
    print("    配色方案键: ['wall', 'door_window', 'unlabeled', 'other']...")
    print("    解析结果: status='auto_labeled', label='wall', is_current=False")
    print("    🎯 优先级3-已标注状态: status='auto_labeled', label='wall'")
    print("    ✅ 标签'wall'映射到颜色: #8B4513")
    print("  🔍 [实体匹配] 查找实体: layer='A-WALL', type='LINE', id=123456")
    print("    ✅ ID匹配成功：组0")
    print("    🎯 使用组0信息: {'status': 'auto_labeled', 'label': 'wall', 'is_current_group': False}")
    print("  实体颜色: A-WALL -> #8B4513")
    print()
    print("✅ 修改后的简化输出:")
    print("  组0: ✅ 优先级3-wall: #8B4513")
    print("  实体颜色: 0 -> #8B4513")
    print()
    print("🎯 简化效果:")
    print("  - 移除了详细的调试信息")
    print("  - 保留了关键的组ID和优先级判断")
    print("  - 保留了最终的颜色映射结果")
    print("  - 输出更加简洁清晰")

def provide_output_summary():
    """提供输出总结"""
    
    print("\n📋 简化输出总结")
    print("=" * 25)
    
    print("✅ 保留的关键信息:")
    print("  1. **组ID** - 明确显示哪个组")
    print("  2. **优先级判断** - 显示命中的优先级和类型")
    print("  3. **最终颜色** - 显示映射的颜色值")
    print("  4. **实体颜色映射** - 组ID到颜色的对应关系")
    print()
    print("❌ 移除的冗余信息:")
    print("  - 详细的组信息内容")
    print("  - 配色方案键列表")
    print("  - 解析过程的中间步骤")
    print("  - 实体匹配的详细过程")
    print("  - 各种警告和错误信息")
    print()
    print("🎯 输出格式:")
    print("  组{ID}: ✅ 优先级{N}-{类型}: {颜色}")
    print("  实体颜色: {组ID} -> {颜色}")
    print()
    print("📝 示例:")
    print("  组0: ✅ 优先级3-wall: #8B4513")
    print("  实体颜色: 0 -> #8B4513")
    print()
    print("  组25: ✅ 优先级2-标注中: #FF0000")
    print("  实体颜色: 25 -> #FF0000")

if __name__ == "__main__":
    print("🔍 简化批量概览输出测试程序")
    print("=" * 50)
    
    # 测试简化输出
    output_result = test_simplified_output()
    
    # 演示输出对比
    demonstrate_output_comparison()
    
    # 提供输出总结
    provide_output_summary()
    
    print("\n" + "=" * 50)
    print("🎉 测试总结:")
    
    if output_result:
        print("✅ 简化输出测试通过")
        print("✅ 输出格式已优化")
        print("✅ 保留了关键信息")
        print("✅ 移除了冗余内容")
        print("\n🚀 现在批量概览输出更加简洁清晰：")
        print("  - 只显示组ID、优先级判断和最终颜色")
        print("  - 移除了详细的调试过程")
        print("  - 便于快速查看颜色映射结果")
    else:
        print("❌ 简化输出测试失败")
        print("❌ 需要检查输出格式")
