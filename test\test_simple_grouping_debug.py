#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的分组调试测试
"""

def test_import():
    """测试导入"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_debug_methods():
    """测试调试方法存在性"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        methods = [
            '_debug_grouping_completion',
            '_debug_grouping_completion_delayed',
            '_get_group_color_info'
        ]
        
        for method in methods:
            if hasattr(EnhancedCADAppV2, method):
                print(f"✅ {method}: 存在")
            else:
                print(f"❌ {method}: 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_trigger_code():
    """测试触发代码"""
    try:
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        import inspect
        
        # 检查_process_group_stage_v2方法
        method = getattr(EnhancedCADAppV2, '_process_group_stage_v2', None)
        if method:
            source = inspect.getsource(method)
            
            if "_debug_grouping_completion()" in source:
                print("✅ 立即调试触发: 存在")
            else:
                print("❌ 立即调试触发: 缺失")
                return False
                
            if "_debug_grouping_completion_delayed()" in source:
                print("✅ 延迟调试触发: 存在")
            else:
                print("❌ 延迟调试触发: 缺失")
                return False
            
            return True
        else:
            print("❌ _process_group_stage_v2方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 简单分组调试测试")
    print("="*40)
    
    test1 = test_import()
    test2 = test_debug_methods()
    test3 = test_trigger_code()
    
    print(f"\n" + "="*40)
    print("📊 测试结果")
    print("="*40)
    
    print(f"🧪 导入测试: {'✅ 通过' if test1 else '❌ 失败'}")
    print(f"🧪 调试方法: {'✅ 通过' if test2 else '❌ 失败'}")
    print(f"🧪 触发代码: {'✅ 通过' if test3 else '❌ 失败'}")
    
    if test1 and test2 and test3:
        print(f"\n🎉 所有测试通过！")
        print(f"\n💡 调试功能已正确集成:")
        print(f"1. ✅ 调试方法已添加")
        print(f"2. ✅ 触发代码已集成")
        print(f"3. ✅ 立即和延迟调试都已实现")
        
        print(f"\n🔧 现在您可以:")
        print(f"1. 启动程序: python main_enhanced_with_v2_fill.py")
        print(f"2. 加载DXF文件")
        print(f"3. 完成线条处理")
        print(f"4. 点击'识别分组'按钮")
        print(f"5. 查看控制台的详细调试输出")
        
        print(f"\n📋 期望看到的调试输出:")
        print(f"🔧 [调试] 分组处理完成，开始测试数据输出时间...")
        print(f"🔍 [调试] 立即触发分组完成调试信息...")
        print(f"================================================================================")
        print(f"🎯 ===== 识别分组完成调试信息 =====")
        print(f"================================================================================")
        print(f"📊 分组统计:")
        print(f"  总实体数: XXX")
        print(f"  总组数: YYY")
        print(f"📋 各组详细信息 (总组数: YYY):")
        print(f"  组1: ID=1, 类型=wall, 实体数=XX, 颜色=#8B4513")
        print(f"       主要实体类型=LINE, 主要图层=A-WALL, 状态=自动标注(XX)")
        print(f"  组2: ID=2, 类型=unlabeled, 实体数=YY, 颜色=#808080")
        print(f"       主要实体类型=LINE, 主要图层=A-DOOR, 状态=待标注")
        print(f"  ...")
        print(f"🎯 ===== 分组完成调试信息结束 =====")
        print(f"================================================================================")
        
    else:
        print(f"\n⚠️ 部分测试失败")

if __name__ == "__main__":
    main()
