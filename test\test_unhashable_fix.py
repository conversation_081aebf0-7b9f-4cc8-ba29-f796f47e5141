#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试 unhashable type: 'dict' 错误修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cad_visualizer import CADVisualizer
import matplotlib.pyplot as plt

def test_unhashable_dict_fix():
    """测试 unhashable dict 错误修复"""
    print("🧪 测试 unhashable dict 错误修复")
    print("="*60)
    
    # 创建可视化器
    visualizer = CADVisualizer()
    
    # 创建测试数据
    test_entities = [
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WALL', 'points': [(100, 0), (100, 100)], 'label': '墙体', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-WINDOW', 'points': [(50, -10), (50, 10)], 'label': '门窗', 'auto_labeled': True},
        {'type': 'LINE', 'layer': 'A-DOOR', 'points': [(150, 50), (200, 50)], 'label': '门窗', 'auto_labeled': True},
    ]
    
    # 创建测试组
    test_groups = [
        [test_entities[0], test_entities[1]],  # 墙体组
        [test_entities[2], test_entities[3]]   # 门窗组
    ]
    
    # 创建当前组（用于测试高亮）
    current_group = test_groups[0]
    
    # 创建已标注实体列表
    labeled_entities = [test_entities[0], test_entities[2]]
    
    print(f"测试数据:")
    print(f"  实体数: {len(test_entities)}")
    print(f"  组数: {len(test_groups)}")
    print(f"  当前组实体数: {len(current_group)}")
    print(f"  已标注实体数: {len(labeled_entities)}")
    
    # 测试1: _is_entity_in_group_safe 方法
    print(f"\n🔧 测试1: _is_entity_in_group_safe 方法")
    try:
        entity = test_entities[0]
        group = test_groups[0]
        
        result = visualizer._is_entity_in_group_safe(entity, group)
        print(f"  实体在组中: {result} (期望: True)")
        
        # 测试不在组中的情况
        result2 = visualizer._is_entity_in_group_safe(entity, test_groups[1])
        print(f"  实体不在组中: {result2} (期望: False)")
        
        print(f"  ✅ _is_entity_in_group_safe 方法测试通过")
        
    except Exception as e:
        print(f"  ❌ _is_entity_in_group_safe 方法测试失败: {e}")
        return False
    
    # 测试2: _is_entity_in_labeled_list 方法
    print(f"\n🔧 测试2: _is_entity_in_labeled_list 方法")
    try:
        entity = test_entities[0]
        
        result = visualizer._is_entity_in_labeled_list(entity, labeled_entities)
        print(f"  实体在已标注列表中: {result} (期望: True)")
        
        # 测试不在列表中的情况
        result2 = visualizer._is_entity_in_labeled_list(test_entities[1], labeled_entities)
        print(f"  实体不在已标注列表中: {result2} (期望: False)")
        
        print(f"  ✅ _is_entity_in_labeled_list 方法测试通过")
        
    except Exception as e:
        print(f"  ❌ _is_entity_in_labeled_list 方法测试失败: {e}")
        return False
    
    # 测试3: _get_entity_display_info_enhanced 方法
    print(f"\n🔧 测试3: _get_entity_display_info_enhanced 方法")
    try:
        entity = test_entities[0]
        
        # 测试当前组实体
        color, alpha, linewidth, status = visualizer._get_entity_display_info_enhanced(
            entity, labeled_entities, current_group, test_groups, None, None
        )
        print(f"  当前组实体显示信息: 颜色={color}, 透明度={alpha}, 线宽={linewidth}, 状态={status}")
        
        # 测试已标注实体
        entity2 = test_entities[2]
        color2, alpha2, linewidth2, status2 = visualizer._get_entity_display_info_enhanced(
            entity2, labeled_entities, None, test_groups, None, None
        )
        print(f"  已标注实体显示信息: 颜色={color2}, 透明度={alpha2}, 线宽={linewidth2}, 状态={status2}")
        
        print(f"  ✅ _get_entity_display_info_enhanced 方法测试通过")
        
    except Exception as e:
        print(f"  ❌ _get_entity_display_info_enhanced 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试4: visualize_overview 方法（关键测试）
    print(f"\n🔧 测试4: visualize_overview 方法（关键测试）")
    try:
        # 这是最容易出现 unhashable dict 错误的地方
        visualizer.visualize_overview(
            test_entities,
            current_group,
            labeled_entities,
            processor=None
        )
        
        print(f"  ✅ visualize_overview 方法测试通过")
        
    except Exception as e:
        print(f"  ❌ visualize_overview 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试5: visualize_entity_group 方法
    print(f"\n🔧 测试5: visualize_entity_group 方法")
    try:
        visualizer.visualize_entity_group(current_group, {})
        
        print(f"  ✅ visualize_entity_group 方法测试通过")
        
    except Exception as e:
        print(f"  ❌ visualize_entity_group 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🧪 测试边界情况")
    print("="*60)
    
    # 创建可视化器
    visualizer = CADVisualizer()
    
    # 测试空列表
    print(f"1. 测试空列表:")
    try:
        entity = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        result = visualizer._is_entity_in_group_safe(entity, [])
        print(f"   空列表测试: {result} (期望: False)")
        
    except Exception as e:
        print(f"   ❌ 空列表测试失败: {e}")
        return False
    
    # 测试None值
    print(f"\n2. 测试None值:")
    try:
        entity = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        result = visualizer._is_entity_in_group_safe(entity, None)
        print(f"   None值测试: {result} (期望: False)")
        
    except Exception as e:
        print(f"   ❌ None值测试失败: {e}")
        return False
    
    # 测试混合类型列表
    print(f"\n3. 测试混合类型列表:")
    try:
        entity = {'type': 'LINE', 'layer': 'A-WALL', 'points': [(0, 0), (100, 0)]}
        mixed_list = [entity, "string", 123, None]
        result = visualizer._is_entity_in_group_safe(entity, mixed_list)
        print(f"   混合类型列表测试: {result} (期望: True)")
        
    except Exception as e:
        print(f"   ❌ 混合类型列表测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试 unhashable dict 错误修复")
    
    try:
        # 基本功能测试
        success1 = test_unhashable_dict_fix()
        
        # 边界情况测试
        success2 = test_edge_cases()
        
        print(f"\n" + "="*60)
        if success1 and success2:
            print(f"🎉 所有测试通过！unhashable dict 错误已完全修复。")
            print(f"💡 现在可视化器应该能够正常工作，不会再出现 unhashable dict 错误。")
        else:
            print(f"⚠️ 部分测试失败，可能还需要进一步修复。")
        
        # 关闭图形窗口
        plt.close('all')
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
