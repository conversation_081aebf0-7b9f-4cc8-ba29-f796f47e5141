#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
准确测试界面显示结果
模拟真实的界面显示流程
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_file_processing():
    """测试真实文件处理流程"""
    print("🧪 测试真实文件处理流程")
    print("="*60)
    
    # 查找包含墙体的测试文件
    test_files = [
        r'test-dxf-wall\wall00.dxf',
        r'txt-dxf-2file\wall01.dxf',
        r'txt-dxf-3file\awall01.dxf'
    ]

    # 找到第一个存在的文件
    selected_file = None
    for file_path in test_files:
        if os.path.exists(file_path):
            selected_file = file_path
            break

    if not selected_file:
        print("❌ 没有找到包含墙体的DXF测试文件")
        return None, None

    test_files = [selected_file]
    
    print(f"📁 使用测试文件: {test_files[0]}")
    
    try:
        from main_enhanced import EnhancedCADProcessor
        
        # 创建处理器
        processor = EnhancedCADProcessor()
        print(f"✅ 处理器创建成功")
        
        # 检查初始状态
        print(f"📋 初始状态检查:")
        print(f"  category_mapping: {processor.category_mapping}")
        print(f"  all_groups: {len(processor.all_groups)}")
        print(f"  groups_info: {len(processor.groups_info)}")
        
        # 模拟文件处理流程
        print(f"\n🔧 模拟文件处理流程:")
        
        # 1. 读取文件
        try:
            entities = processor.processor.load_dxf_file(test_files[0])
            print(f"  ✅ 文件读取成功: {len(entities)} 个实体")
            processor.entities = entities
            processor.current_file_entities = entities
        except Exception as e:
            print(f"  ❌ 文件读取失败: {e}")
            return None, None
        
        # 2. 自动处理特殊实体
        try:
            processor._auto_process_special_entities(entities)
            print(f"  ✅ 自动处理完成")
            print(f"    总组数: {len(processor.all_groups)}")
            print(f"    自动标注实体: {len(processor.auto_labeled_entities)}")
        except Exception as e:
            print(f"  ❌ 自动处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
        
        # 3. 更新组信息
        try:
            processor._update_groups_info()
            print(f"  ✅ 组信息更新完成: {len(processor.groups_info)} 个组")
        except Exception as e:
            print(f"  ❌ 组信息更新失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
        
        return processor, test_files[0]
        
    except Exception as e:
        print(f"❌ 文件处理流程失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_group_list_display(processor):
    """测试组列表显示"""
    print(f"\n🧪 测试组列表显示")
    print("="*60)
    
    if not processor or not processor.groups_info:
        print("❌ 处理器或组信息为空")
        return False
    
    print(f"📋 组信息详情:")
    for i, info in enumerate(processor.groups_info):
        print(f"  组 {i+1}:")
        print(f"    原始数据: {info}")
        
        # 模拟UI显示逻辑（从实际代码中提取）
        status = info.get('status', 'unknown')
        group_type = info.get('group_type', 'unknown')
        entity_count = info.get('entity_count', 0)
        
        # 状态文本映射
        if status == 'auto_labeled':
            status_text = '自动标注'
            tag = 'auto_labeled'
        elif status == 'labeled':
            status_text = '已标注'
            tag = 'labeled'
        elif status == 'pending':
            status_text = '待处理'
            tag = 'pending'
        else:
            status_text = status
            tag = 'unknown'
        
        # 类型文本映射（关键测试点）
        if hasattr(processor, 'category_mapping') and processor.category_mapping:
            type_text = processor.category_mapping.get(group_type, group_type)
        else:
            type_text = group_type
        
        # 模拟最终显示文本
        display_text = f"{status_text} - {type_text} ({entity_count} 个实体)"
        print(f"    显示文本: {display_text}")
        
        # 验证关键点
        if group_type in ['wall', 'door_window', 'other']:
            expected_type_text = processor.category_mapping.get(group_type, group_type)
            if type_text == expected_type_text:
                print(f"    ✅ 类型映射正确: {group_type} -> {type_text}")
            else:
                print(f"    ❌ 类型映射错误: {group_type} -> {type_text}, 期望: {expected_type_text}")
                return False
        else:
            print(f"    ⚠️ 未知组类型: {group_type}")
    
    # 统计各类组
    wall_groups = [g for g in processor.groups_info if g.get('group_type') == 'wall']
    door_window_groups = [g for g in processor.groups_info if g.get('group_type') == 'door_window']
    other_groups = [g for g in processor.groups_info if g.get('group_type') == 'other']
    auto_labeled_groups = [g for g in processor.groups_info if g.get('status') == 'auto_labeled']
    
    print(f"\n📈 组统计:")
    print(f"  墙体组: {len(wall_groups)} 个")
    print(f"  门窗组: {len(door_window_groups)} 个")
    print(f"  其他组: {len(other_groups)} 个")
    print(f"  自动标注组: {len(auto_labeled_groups)} 个")
    
    # 验证是否有墙体和门窗组
    if len(wall_groups) == 0:
        print(f"  ❌ 没有墙体组被识别")
        return False
    
    if len(door_window_groups) == 0:
        print(f"  ❌ 没有门窗组被识别")
        return False
    
    print(f"  ✅ 组列表显示测试通过")
    return True

def test_entity_group_preview(processor):
    """测试实体组预览"""
    print(f"\n🧪 测试实体组预览")
    print("="*60)
    
    if not processor or not processor.all_groups:
        print("❌ 处理器或组数据为空")
        return False
    
    try:
        from cad_visualizer import CADVisualizer
        
        # 创建可视化器
        visualizer = CADVisualizer()
        print(f"✅ 可视化器创建成功")
        
        # 测试前几个组的预览
        test_count = min(3, len(processor.all_groups))
        print(f"📋 测试 {test_count} 个组的预览:")
        
        for i in range(test_count):
            group = processor.all_groups[i]
            print(f"\n  测试组 {i+1}:")
            print(f"    原始组类型: {type(group)}")
            
            # 清理组数据
            if hasattr(processor, '_clean_group_data'):
                cleaned_group = processor._clean_group_data(group)
                print(f"    清理后实体数: {len(cleaned_group)}")
                
                if not cleaned_group:
                    print(f"    ⚠️ 清理后组为空，跳过")
                    continue
                
                # 显示实体详情
                print(f"    实体详情:")
                for j, entity in enumerate(cleaned_group[:3]):  # 只显示前3个实体
                    if isinstance(entity, dict):
                        print(f"      实体 {j+1}: {entity.get('type', 'unknown')} - {entity.get('layer', 'unknown')}")
                    else:
                        print(f"      实体 {j+1}: 无效实体 - {type(entity)}")
                
                # 测试可视化
                try:
                    visualizer.visualize_entity_group(cleaned_group, processor.category_mapping or {})
                    print(f"    ✅ 组预览可视化成功")
                except Exception as e:
                    print(f"    ❌ 组预览可视化失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            else:
                print(f"    ❌ _clean_group_data 方法不存在")
                return False
        
        print(f"\n✅ 实体组预览测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 实体组预览测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_overview_display(processor):
    """测试全图概览显示"""
    print(f"\n🧪 测试全图概览显示")
    print("="*60)
    
    if not processor:
        print("❌ 处理器为空")
        return False
    
    try:
        from cad_visualizer import CADVisualizer
        
        # 创建可视化器
        visualizer = CADVisualizer()
        print(f"✅ 可视化器创建成功")
        
        # 检查必要数据
        print(f"📋 数据检查:")
        print(f"  总实体数: {len(processor.current_file_entities) if hasattr(processor, 'current_file_entities') else 0}")
        print(f"  总组数: {len(processor.all_groups) if hasattr(processor, 'all_groups') else 0}")
        print(f"  自动标注实体数: {len(processor.auto_labeled_entities) if hasattr(processor, 'auto_labeled_entities') else 0}")
        print(f"  已标注实体数: {len(processor.labeled_entities) if hasattr(processor, 'labeled_entities') else 0}")
        
        if not hasattr(processor, 'current_file_entities') or not processor.current_file_entities:
            print("❌ 缺少文件实体数据")
            return False
        
        if not hasattr(processor, 'all_groups') or not processor.all_groups:
            print("❌ 缺少组数据")
            return False
        
        # 准备测试数据
        current_group = processor.all_groups[0] if processor.all_groups else []
        if hasattr(processor, '_clean_group_data'):
            current_group = processor._clean_group_data(current_group)
        
        # 合并已标注实体
        labeled_entities = []
        if hasattr(processor, 'auto_labeled_entities'):
            labeled_entities.extend(processor.auto_labeled_entities)
        if hasattr(processor, 'labeled_entities'):
            labeled_entities.extend(processor.labeled_entities)
        
        print(f"  当前组实体数: {len(current_group)}")
        print(f"  已标注实体总数: {len(labeled_entities)}")
        
        # 分析已标注实体的分布
        if labeled_entities:
            wall_entities = [e for e in labeled_entities if isinstance(e, dict) and e.get('label') == '墙体']
            door_window_entities = [e for e in labeled_entities if isinstance(e, dict) and e.get('label') == '门窗']
            other_entities = [e for e in labeled_entities if isinstance(e, dict) and e.get('label') not in ['墙体', '门窗']]
            
            print(f"  已标注实体分布:")
            print(f"    墙体实体: {len(wall_entities)} 个")
            print(f"    门窗实体: {len(door_window_entities)} 个")
            print(f"    其他实体: {len(other_entities)} 个")
            
            if len(wall_entities) == 0:
                print(f"    ❌ 没有墙体实体被标注")
                return False
            
            if len(door_window_entities) == 0:
                print(f"    ❌ 没有门窗实体被标注")
                return False
        
        # 测试全图概览
        print(f"\n🌍 测试全图概览可视化:")
        try:
            visualizer.visualize_overview(
                processor.current_file_entities,
                current_group,
                labeled_entities,
                processor=processor
            )
            print(f"  ✅ 全图概览可视化成功")
            return True
            
        except Exception as e:
            print(f"  ❌ 全图概览可视化失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 全图概览测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_integration_with_tkinter():
    """测试与Tkinter的集成"""
    print(f"\n🧪 测试与Tkinter的集成")
    print("="*60)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建测试组件
        tree = ttk.Treeview(root)
        
        # 模拟组列表数据插入
        test_groups_info = [
            {
                'index': 0,
                'label': '墙体',
                'entity_count': 4,
                'status': 'auto_labeled',
                'group_type': 'wall',
                'layer': 'A-WALL'
            },
            {
                'index': 1,
                'label': '门窗',
                'entity_count': 2,
                'status': 'auto_labeled',
                'group_type': 'door_window',
                'layer': 'A-WINDOW'
            },
            {
                'index': 2,
                'label': 'group_2',
                'entity_count': 1,
                'status': 'pending',
                'group_type': 'other',
                'layer': 'A-TEXT'
            }
        ]
        
        # 模拟category_mapping
        category_mapping = {
            'wall': '墙体',
            'door_window': '门窗',
            'other': '其他'
        }
        
        print(f"📋 模拟组列表插入:")
        
        # 清空树
        for item in tree.get_children():
            tree.delete(item)
        
        # 插入测试数据
        for info in test_groups_info:
            status = info.get('status', 'unknown')
            group_type = info.get('group_type', 'unknown')
            entity_count = info.get('entity_count', 0)
            
            # 状态文本
            if status == 'auto_labeled':
                status_text = '自动标注'
                tag = 'auto_labeled'
            elif status == 'pending':
                status_text = '待处理'
                tag = 'pending'
            else:
                status_text = status
                tag = 'unknown'
            
            # 类型文本
            type_text = category_mapping.get(group_type, group_type)
            
            # 组ID
            group_id = f"组{info.get('index', 0) + 1}"
            
            # 插入到树中
            item_id = tree.insert('', 'end', text=group_id,
                                values=(status_text, type_text, entity_count),
                                tags=(tag,))
            
            print(f"  插入: {group_id} | {status_text} | {type_text} | {entity_count} 个实体")
        
        # 验证插入结果
        children = tree.get_children()
        print(f"\n📊 插入结果验证:")
        print(f"  插入的项目数: {len(children)}")
        
        for i, child in enumerate(children):
            values = tree.item(child)
            print(f"  项目 {i+1}: {values}")
        
        # 清理
        root.destroy()
        
        print(f"✅ Tkinter集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Tkinter集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始准确的界面显示测试")
    
    try:
        # 1. 测试真实文件处理
        processor, test_file = test_real_file_processing()
        
        if not processor:
            print("❌ 文件处理失败，无法继续测试")
            return
        
        # 2. 测试组列表显示
        test1_success = test_group_list_display(processor)
        
        # 3. 测试实体组预览
        test2_success = test_entity_group_preview(processor)
        
        # 4. 测试全图概览显示
        test3_success = test_overview_display(processor)
        
        # 5. 测试UI集成
        test4_success = test_ui_integration_with_tkinter()
        
        print(f"\n" + "="*60)
        print(f"📊 准确测试结果总结:")
        print(f"  组列表显示: {'✅ 通过' if test1_success else '❌ 失败'}")
        print(f"  实体组预览: {'✅ 通过' if test2_success else '❌ 失败'}")
        print(f"  全图概览显示: {'✅ 通过' if test3_success else '❌ 失败'}")
        print(f"  UI集成: {'✅ 通过' if test4_success else '❌ 失败'}")
        
        if all([test1_success, test2_success, test3_success, test4_success]):
            print(f"\n🎉 所有界面显示测试通过！")
            print(f"💡 界面应该能够正确显示:")
            print(f"   - 组列表中的墙体和门窗类型")
            print(f"   - CAD实体组预览")
            print(f"   - CAD实体全图概览")
        else:
            print(f"\n⚠️ 部分界面显示测试失败，需要进一步修复。")
            
            # 提供具体的修复建议
            if not test1_success:
                print(f"🔧 组列表显示问题可能原因:")
                print(f"   - 组信息更新逻辑有问题")
                print(f"   - category_mapping 映射不正确")
                print(f"   - 组类型识别错误")
            
            if not test2_success:
                print(f"🔧 实体组预览问题可能原因:")
                print(f"   - 组数据清理不完整")
                print(f"   - 可视化器实体处理有问题")
                print(f"   - 实体格式不正确")
            
            if not test3_success:
                print(f"🔧 全图概览显示问题可能原因:")
                print(f"   - 已标注实体数据不完整")
                print(f"   - 实体标签设置有问题")
                print(f"   - 可视化器概览逻辑有问题")
        
    except Exception as e:
        print(f"❌ 准确测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
