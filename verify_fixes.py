#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证修复是否完成
"""

def verify_batch_overview_fix():
    """验证批量概览颜色修复"""
    print("🔧 验证批量概览颜色修复...")
    
    try:
        from visualization_performance_fix import VisualizationPerformanceFix
        
        fix = VisualizationPerformanceFix()
        
        # 测试组信息颜色获取方法是否存在
        if hasattr(fix, '_get_color_from_group_info'):
            print("  ✅ _get_color_from_group_info 方法存在")
            
            # 测试方法功能
            group_info = {'status': 'labeled', 'label': 'wall', 'is_current_group': False}
            color_scheme = {'wall': '#8B4513', 'current_group': '#FF0000', 'unlabeled': '#C0C0C0'}
            
            color = fix._get_color_from_group_info(group_info, color_scheme)
            print(f"  ✅ 颜色获取测试通过: {color}")
            
            if color == '#8B4513':
                print("  ✅ 基于组信息的颜色获取正确")
                return True
            else:
                print(f"  ❌ 颜色获取错误: 期望 #8B4513, 实际 {color}")
                return False
        else:
            print("  ❌ _get_color_from_group_info 方法不存在")
            return False
            
    except Exception as e:
        print(f"  ❌ 批量概览颜色修复验证失败: {e}")
        return False

def verify_legend_cleanup():
    """验证索引图清理"""
    print("\n🧹 验证索引图清理...")
    
    try:
        # 检查cad_visualizer.py
        with open('cad_visualizer.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '已弃用' in content and '_create_color_index_chart' in content:
            print("  ✅ cad_visualizer.py 中的重复方法已标记为弃用")
            
            # 检查方法是否简化
            start_pos = content.find('def _create_color_index_chart')
            if start_pos != -1:
                end_pos = content.find('def ', start_pos + 1)
                if end_pos == -1:
                    end_pos = len(content)
                
                method_content = content[start_pos:end_pos]
                
                if 'pass' in method_content and len(method_content.split('\n')) < 10:
                    print("  ✅ 重复方法已正确简化")
                    return True
                else:
                    print("  ⚠️ 重复方法可能未完全简化")
                    return False
        else:
            print("  ❌ 重复方法标记未找到")
            return False
            
    except Exception as e:
        print(f"  ❌ 索引图清理验证失败: {e}")
        return False

def verify_main_legend_method():
    """验证主索引图方法"""
    print("\n📋 验证主索引图方法...")
    
    try:
        with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '_draw_legend_content' in content:
            # 查找方法内容
            start_pos = content.find('def _draw_legend_content')
            if start_pos != -1:
                end_pos = content.find('def ', start_pos + 1)
                if end_pos == -1:
                    end_pos = len(content)
                
                method_content = content[start_pos:end_pos]
                
                # 检查修复标记
                fixes_found = []
                if '🔧 修复' in method_content:
                    fixes_found.append("修复标记")
                if '网格布局' in method_content or 'cols = 2' in method_content:
                    fixes_found.append("网格布局")
                if '去除分类标题' in method_content or '无标题' in method_content:
                    fixes_found.append("去除分类标题")
                
                if len(fixes_found) >= 2:
                    print(f"  ✅ 主索引图方法包含修复: {', '.join(fixes_found)}")
                    return True
                else:
                    print(f"  ⚠️ 主索引图方法修复不完整: {', '.join(fixes_found)}")
                    return False
        else:
            print("  ❌ 主索引图方法未找到")
            return False
            
    except Exception as e:
        print(f"  ❌ 主索引图方法验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 验证修复完成状态")
    print("=" * 50)
    
    # 验证各项修复
    fix1 = verify_batch_overview_fix()
    fix2 = verify_legend_cleanup()
    fix3 = verify_main_legend_method()
    
    print("\n" + "=" * 50)
    print("📊 验证结果总结:")
    
    results = [
        ("批量概览颜色修复", fix1),
        ("索引图重复清理", fix2),
        ("主索引图方法", fix3)
    ]
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有修复验证通过！")
        print("✅ 批量概览将显示正确颜色")
        print("✅ 索引图显示统一清晰")
        print("✅ 系统整体协调一致")
    else:
        print("⚠️ 部分修复需要进一步检查")
        print("请查看上述详细信息")

if __name__ == "__main__":
    main()
