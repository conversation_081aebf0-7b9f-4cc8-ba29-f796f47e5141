#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试视图颜色增加功能
验证在组详细信息中是否正确显示了视图颜色
"""

import os
import sys
import time
import tkinter as tk

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_view_color_addition():
    """测试视图颜色增加功能"""
    print("🧪 测试视图颜色增加功能")
    print("=" * 80)
    
    try:
        # 导入主应用
        from main_enhanced_with_v2_fill import EnhancedCADAppV2
        
        # 创建根窗口
        root = tk.Tk()
        root.title("视图颜色测试")
        root.geometry("1400x900")
        
        print("✅ 创建根窗口成功")
        
        # 创建应用实例
        app = EnhancedCADAppV2(root)
        print("✅ 创建应用实例成功")
        
        # 更新界面确保初始化完成
        root.update()
        time.sleep(0.5)
        
        # 创建测试数据
        print("\n📊 创建测试数据")
        print("-" * 60)
        
        test_entities = []
        entity_id = 1
        
        # 创建墙体组
        wall_group = []
        for i in range(3):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': 'A-WALL',
                'start': [i * 50, 0],
                'end': [i * 50 + 40, 0],
                'length': 40,
                'label': 'wall',
                'auto_labeled': True,
                'color': 1
            }
            wall_group.append(entity)
            test_entities.append(entity)
            entity_id += 1
        
        # 创建门窗组
        door_window_group = []
        for i in range(2):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': 'A-WINDOW',
                'start': [i * 30, 60],
                'end': [i * 30 + 25, 60],
                'length': 25,
                'label': 'door_window',
                'auto_labeled': True,
                'color': 2
            }
            door_window_group.append(entity)
            test_entities.append(entity)
            entity_id += 1
        
        # 创建家具组（待标注）
        furniture_group = []
        for i in range(2):
            entity = {
                'id': entity_id,
                'type': 'LINE',
                'layer': 'FURNITURE',
                'start': [i * 40, 120],
                'end': [i * 40 + 35, 120],
                'length': 35,
                'label': 'furniture',
                'auto_labeled': False,
                'color': 3
            }
            furniture_group.append(entity)
            test_entities.append(entity)
            entity_id += 1
        
        all_groups = [wall_group, door_window_group, furniture_group]
        
        print(f"✅ 创建测试数据完成:")
        print(f"   总实体数: {len(test_entities)}")
        print(f"   总组数: {len(all_groups)}")
        print(f"   墙体组: {len(wall_group)} 个实体")
        print(f"   门窗组: {len(door_window_group)} 个实体")
        print(f"   家具组: {len(furniture_group)} 个实体")
        
        # 设置处理器数据
        print("\n🔧 设置处理器数据")
        print("-" * 60)
        
        if hasattr(app, 'processor') and app.processor:
            app.processor.raw_entities = test_entities
            app.processor.current_file_entities = test_entities.copy()
            app.processor.all_groups = all_groups
            app.processor.auto_labeled_entities = wall_group + door_window_group
            app.processor.labeled_entities = furniture_group
            app.processor.pending_manual_groups = []
            app.processor.current_manual_group_index = 0
            app.processor.manual_grouping_mode = False
            
            print(f"✅ 处理器数据设置完成")
            print(f"   自动标注实体: {len(app.processor.auto_labeled_entities)}")
            print(f"   手动标注实体: {len(app.processor.labeled_entities)}")
            print(f"   手动标注模式: {app.processor.manual_grouping_mode}")
        
        # 测试1：调用_debug_grouping_completion方法
        print("\n🧪 测试1：调用_debug_grouping_completion方法")
        print("-" * 60)
        
        if hasattr(app, '_debug_grouping_completion'):
            print("调用 _debug_grouping_completion()...")
            app._debug_grouping_completion()
            print("✅ _debug_grouping_completion() 调用完成")
        else:
            print("❌ _debug_grouping_completion 方法不存在")
        
        # 测试2：直接测试_get_group_view_color方法
        print("\n🧪 测试2：直接测试_get_group_view_color方法")
        print("-" * 60)
        
        if hasattr(app, '_get_group_view_color'):
            print("测试 _get_group_view_color() 方法...")
            
            # 创建模拟的组信息
            test_groups_info = [
                {
                    'group_id': 0,
                    'entity_count': len(wall_group),
                    'status': 'auto_labeled',
                    'label': 'wall',
                    'layer': 'A-WALL',
                    'group_type': 'wall'
                },
                {
                    'group_id': 1,
                    'entity_count': len(door_window_group),
                    'status': 'auto_labeled',
                    'label': 'door_window',
                    'layer': 'A-WINDOW',
                    'group_type': 'door_window'
                },
                {
                    'group_id': 2,
                    'entity_count': len(furniture_group),
                    'status': 'labeled',
                    'label': 'furniture',
                    'layer': 'FURNITURE',
                    'group_type': 'furniture'
                }
            ]
            
            for i, group_info in enumerate(test_groups_info):
                try:
                    view_color = app._get_group_view_color(i, group_info)
                    display_color = app._get_group_display_color(i, group_info)
                    
                    print(f"   组{i+1}:")
                    print(f"     显示颜色: {display_color}")
                    print(f"     视图颜色: {view_color}")
                    
                    if view_color == display_color:
                        print(f"     ✅ 颜色一致")
                    else:
                        print(f"     ⚠️ 颜色不同")
                        
                except Exception as e:
                    print(f"   组{i+1}: ❌ 获取颜色失败 - {e}")
        else:
            print("❌ _get_group_view_color 方法不存在")
        
        # 测试3：验证调试输出格式
        print("\n🧪 测试3：验证调试输出格式")
        print("-" * 60)
        
        # 检查源代码中是否包含"视图颜色"
        try:
            with open('main_enhanced_with_v2_fill.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '视图颜色:' in content:
                print("✅ 源代码中包含'视图颜色'输出")
            else:
                print("❌ 源代码中未找到'视图颜色'输出")
            
            if '_get_group_view_color' in content:
                print("✅ 源代码中包含'_get_group_view_color'方法")
            else:
                print("❌ 源代码中未找到'_get_group_view_color'方法")
                
        except Exception as e:
            print(f"❌ 检查源代码失败: {e}")
        
        print(f"\n🎉 视图颜色增加功能测试完成！")
        print("=" * 80)
        
        # 保持窗口打开一段时间以便观察
        print("\n⏰ 窗口将在3秒后关闭...")
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        try:
            root.destroy()
        except:
            pass
        return False

def verify_output_format():
    """验证输出格式"""
    print("\n🔍 验证预期的输出格式")
    print("-" * 60)
    
    expected_format = """
📋 所有组的详细信息:
   组 1:
     实体数: 12
     类型: wall
     状态: auto_labeled
     标签: wall
     图层: A-WALL
     显示颜色: #8B4513
     视图颜色: #8B4513
   组 2:
     实体数: 15
     类型: door_window
     状态: auto_labeled
     标签: door_window
     图层: A-WINDOW
     显示颜色: #87CEEB
     视图颜色: #87CEEB
   组 3:
     实体数: 8
     类型: furniture
     状态: labeled
     标签: furniture
     图层: FURNITURE
     显示颜色: #0000FF
     视图颜色: #0000FF
"""
    
    print("预期的输出格式:")
    print(expected_format)

if __name__ == "__main__":
    print("🚀 开始视图颜色增加功能测试")
    
    success = test_view_color_addition()
    verify_output_format()
    
    if success:
        print("\n✅ 测试成功完成")
        print("现在调试输出中包含了视图颜色信息")
    else:
        print("\n❌ 测试失败")
